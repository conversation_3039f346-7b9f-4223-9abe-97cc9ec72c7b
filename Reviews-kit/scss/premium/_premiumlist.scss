.reviewkit_prium_header {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    margin-bottom: 30px;


    .reviewkit_prim_top {
        display: flex;
        gap: 20px;
        flex-wrap: wrap;

        @media screen and (max-width: 450px) {
            gap: 10px;
        }

        .reviewkit_profile_name {
            margin: 0;
        }

        .reviewkit_rating_bg {
            width: 150px;
        }
    }

    .reviewkit_prim_bottom {
        .reviewkit_prim_see {}

        .reviewkit_prim_r_count {
            font-weight: 600;
        }

        .review_us_one {}

        .reviewkit_fpln_star_icon {}

        .place_name {
            font-weight: 600;
        }
    }
}

// singel item
.reviewkit_prium_list_layout {
    .reviewkit_prium_list_single_item {
        padding: 15px;
        border-radius: 6px;
        background-color: #fafafa;
        margin-bottom: 30px;

        .reviewkit_ratting_author_time_wrap {

            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;

            .reviewkit_ratting_author_left {

                display: flex;
                align-items: center;
                flex-wrap: wrap;
                gap: 10px;

                .reviewkit_star_rating {
                    margin-bottom: 0;

                    label {
                        font-size: 30px;
                    }
                }

                .reviewkit_ratting_author {
                    span {}
                }
            }

            .reviewkit_ratting_time_right {
                P {
                    margin: 0;
                    color: #98999a;
                }
            }
        }

        .reviewkit_title {
            font-size: 20px;
            font-weight: 600;
            line-height: 1;
            margin: 0 0 15px;
        }

        .reviewkit_pra {
            font-size: 16px;
            line-height: 24px;
            margin: 0;
        }
    }

}