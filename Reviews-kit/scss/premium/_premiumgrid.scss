// review header
@use "../variables";

.reviewkit_header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;

    img {
        width: 150px;
    }

    .reviewkit_profile_name {
        margin: 0;
    }

}

// review profile
.reviewkit_profile_review {
    position: relative;
    display: flex;
    gap: 5px;
    align-items: center;

    margin-bottom: 15px;

    .reviewkit_review_secore {
        font-weight: 700;
    }

    .reviewkit_review_img {
        img {
            width: 150px;
        }
    }

}

.reviewkit_prium_grid_layout {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;

}

// display: flex;
// align-items: center;
// justify-content: space-between;
// flex-wrap: wrap;

// single item
.reviewkit_prium_grid__single_item {
    padding: 15px;
    border-radius: 6px;
    background: #fafafa;

    // border: 1px solid $primary-color;
    // margin-bottom: 15px;
    .reviewkit_ratting_author_time_wrap {

        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;


        .reviewkit_ratting_author_left {
            display: flex;
            flex-direction: column;
            gap: 6px;
            margin-bottom: 15px;

            .reviewkit_star_rating {
                margin-bottom: 0;

                label {
                    font-size: 30px;
                }

            }

            .reviewkit_ratting_author {
                color: #98999a;

                span {
                    color: #484849;
                }
            }
        }

        .reviewkit_ratting_time_right {
            P {
                margin: 0;
                color: #98999a;
            }
        }
    }

    .reviewkit_title {
        font-size: 20px;
        font-weight: 600;
        line-height: 1;
        margin: 0 0 15px;
    }

    .reviewkit_pra {
        font-size: 16px;
        line-height: 24px;
        margin: 0;
    }

}