@use "variables";

.reviewkit_fpln_starter {
    display: flex;
    flex-direction: column;
    align-items: center;

    .reviewkit_fpln_inner_top {
        margin-bottom: 6px;

        .mirc_see {}

        .mirc_r_count {
            font-weight: 600;
        }

        .review_us_one {}
    }

    .reviewkit_bg {
        img {
            width: 150px;
            margin-bottom: 4px;
        }
    }

    .reviewkit_toltip_wrap {
        display: flex;
        align-items: center;

        .reviewkit_logo_container {
            position: relative;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
                transform: translateY(-2px);
            }

            .reviewkit_star {
                width: 20px;
                height: 20px;
                background-color: variables.$trustpilot-color;
                clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
                transition: transform 0.2s ease;
            }

            .reviewkit_text {
                font-size: 18px;
                font-weight: 600;
                color: variables.$body-text-color;
                letter-spacing: -0.5px;
            }

            &:hover .reviewkit_star {
                transform: scale(1.1);
            }

            .reviewkit_tooltip {
                position: absolute;
                top: 100%;
                left: 50%;
                transform: translateX(-50%);
                background: variables.$body-text-color;
                color: variables.$color-white;
                padding: 16px 20px;
                border-radius: 8px;
                font-size: 14px;
                white-space: nowrap;
                opacity: 0;
                visibility: hidden;
                -webkit-transition: all 0.3s ease;
                transition: all 0.3s ease;
                z-index: 10;
                margin-bottom: 0;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                text-align: center;

                &::after {
                    content: "";
                    position: absolute;
                    top: -16px;
                    left: 50%;
                    transform: translateX(-50%);
                    border: 8px solid transparent;
                    border-bottom-color: variables.$body-text-color;
                }

                .reviewkit_tooltip_content {
                    margin-bottom: 6px;
                }

            }

            &:hover .reviewkit_tooltip {
                opacity: 1;
                visibility: visible;
                transform: translateX(-50%) translateY(7px);
            }

            .reviewkit_tooltip_link {
                color: variables.$trustpilot-color;
                text-decoration: none;
                font-weight: 500;
                transition: color 0.3s ease;

                &:hover {
                    text-decoration: underline;
                }
            }

        }

    }
}