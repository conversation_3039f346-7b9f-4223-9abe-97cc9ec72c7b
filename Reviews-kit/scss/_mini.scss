@use "variables";

.reviewkit_fpln_mini {
    display: inline-flex;
    flex-wrap: wrap;
    flex-direction: column;

    .reviewkit_fpln_inner_top {
        margin-bottom: 8px;

        .reviewkit_fpln_star_icon {
            margin-right: 0;
        }

        .place_name {}

    }

    .reviewkit_bg {
        img {
            width: 150px;
            margin-bottom: 4px;
        }
    }

    .reviewkit_fpln_inner_bottom {
        display: flex;
        gap: 20px;

        .reviewkit_left_reviews {
            position: relative;

            &::before {
                position: absolute;
                content: "";
                top: 50%;
                right: -10px;
                height: 14px;
                transform: translateY(-50%);
                width: 1px;
                background: variables.$body-text-color;
            }

            .review_us_one {}

            .reviewkit_orignal_rcount {
                font-weight: 600;
            }
        }

        .reviewkit_review_area {
            .reviewkit_out_of {
                font-weight: 600;
            }

            .reviewkit_reviews {}

        }
    }

}