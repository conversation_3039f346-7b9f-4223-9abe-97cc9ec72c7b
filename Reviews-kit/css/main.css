.reviewkit_fpln_common {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 5px;
  padding: 22px 26px;
  border-radius: 4px;
  border: 1px solid #00b67a;
  text-decoration: none;
  font-size: 16px;
  font-weight: 600;
  color: #14223b;
}

.reviewkit_fpln_star_icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  fill: #00b67a;
}

/* Star Rating Container */
.reviewkit_star_rating {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
      -ms-flex-direction: row-reverse;
          flex-direction: row-reverse;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: start;
  gap: 5px;
  margin-bottom: 15px;
}

/* Hide radio buttons */
.reviewkit_star_rating input {
  display: none;
}

/* Style star labels */
.reviewkit_star_rating label {
  font-size: 40px;
  line-height: 1;
  color: #ddd;
  cursor: pointer;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

/* Hover effect */
.reviewkit_star_rating label:hover {
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
}

/* Default hover color (yellow) */
.reviewkit_star_rating label:hover,
.reviewkit_star_rating label:hover ~ label {
  color: #ffce00;
}

/* 1 Star Selected - RED */
.reviewkit_star_rating input[value="1"]:checked ~ label[for=star1] {
  color: #ff3722;
}

/* 2 Stars Selected - ORANGE */
.reviewkit_star_rating input[value="2"]:checked ~ label[for=star2],
.reviewkit_star_rating input[value="2"]:checked ~ label[for=star1] {
  color: #ff8622;
}

/* 3 Stars Selected - YELLOW */
.reviewkit_star_rating input[value="3"]:checked ~ label[for=star3],
.reviewkit_star_rating input[value="3"]:checked ~ label[for=star2],
.reviewkit_star_rating input[value="3"]:checked ~ label[for=star1] {
  color: #ffce00;
}

/* 4 Stars Selected - BLUE */
.reviewkit_star_rating input[value="4"]:checked ~ label[for=star4],
.reviewkit_star_rating input[value="4"]:checked ~ label[for=star3],
.reviewkit_star_rating input[value="4"]:checked ~ label[for=star2],
.reviewkit_star_rating input[value="4"]:checked ~ label[for=star1] {
  color: #73cf11;
}

/* 5 Stars Selected - GREEN */
.reviewkit_star_rating input[value="5"]:checked ~ label[for=star5],
.reviewkit_star_rating input[value="5"]:checked ~ label[for=star4],
.reviewkit_star_rating input[value="5"]:checked ~ label[for=star3],
.reviewkit_star_rating input[value="5"]:checked ~ label[for=star2],
.reviewkit_star_rating input[value="5"]:checked ~ label[for=star1] {
  color: #00b67a;
}

/* Override hover when rating is selected */
.reviewkit_star_rating input[value="1"]:checked ~ label:hover,
.reviewkit_star_rating input[value="1"]:checked ~ label:hover ~ label {
  color: #ff3722;
}

.reviewkit_star_rating input[value="2"]:checked ~ label:hover,
.reviewkit_star_rating input[value="2"]:checked ~ label:hover ~ label {
  color: #ff8622;
}

.reviewkit_star_rating input[value="3"]:checked ~ label:hover,
.reviewkit_star_rating input[value="3"]:checked ~ label:hover ~ label {
  color: #ffce00;
}

.reviewkit_star_rating input[value="4"]:checked ~ label:hover,
.reviewkit_star_rating input[value="4"]:checked ~ label:hover ~ label {
  color: #73cf11;
}

.reviewkit_star_rating input[value="5"]:checked ~ label:hover,
.reviewkit_star_rating input[value="5"]:checked ~ label:hover ~ label {
  color: #00b67a;
}

.reviewkit_fpln_mirc .mirc_see {
  font-weight: 500;
}
.reviewkit_fpln_mirc .mirc_r_count {
  font-weight: 700;
}
.reviewkit_fpln_mirc .review_us_one {
  font-weight: 500;
}
.reviewkit_fpln_mirc .place_name {
  font-weight: 600;
}

.reviewkit_fpln_mcb_wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.reviewkit_fpln_mcb_wrap .reviewkit_fpln_mcb_left {
  background-color: #00b67a;
  color: #fff;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 6px;
  padding: 6px 10px;
  text-decoration: none;
}
.reviewkit_fpln_mcb_wrap .reviewkit_fpln_mcb_left .reviewkit_fpln_star_icon {
  fill: #fff;
}
.reviewkit_fpln_mcb_wrap .reviewkit_fpln_mcb_left .place_name {
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  line-height: 1;
}
.reviewkit_fpln_mcb_wrap .reviewkit_fpln_mcb_right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border: 1px solid #cfd0cf;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background-color: #fff;
  font-size: 16px;
  line-height: 1;
  color: #555;
  padding: 6px 10px;
}

.reviewkit_fpln_mc {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 15px;
}
.reviewkit_fpln_mc .reviewkit_fpln_mc_inner_left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 15px;
}
.reviewkit_fpln_mc .reviewkit_fpln_mc_inner_left .review_us_one {
  font-weight: 700;
}
.reviewkit_fpln_mc .reviewkit_fpln_mc_inner_left .reviewkit_star_rating {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  margin-bottom: 0;
}
.reviewkit_fpln_mc .reviewkit_fpln_mc_inner_left .reviewkit_star_rating label {
  font-size: 16px;
  padding: 4px;
  color: #fff;
  background: #00b67a;
}
.reviewkit_fpln_mc .reviewkit_fpln_mc_inner_right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 6px;
}
.reviewkit_fpln_mc .reviewkit_fpln_mc_inner_right .review_us_one {
  font-weight: 400;
}
.reviewkit_fpln_mc .reviewkit_fpln_mc_inner_right .reviewkit_fpln_star_icon {
  margin-right: 0;
}

.reviewkit_fpln_mcs {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 15px;
}
.reviewkit_fpln_mcs .reviewkit_fpln_mc_inner_left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 15px;
}
.reviewkit_fpln_mcs .reviewkit_fpln_mc_inner_left .review_us_one {
  font-weight: 700;
}
.reviewkit_fpln_mcs .reviewkit_fpln_mc_inner_left .reviewkit_star_rating {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  margin-bottom: 0;
}
.reviewkit_fpln_mcs .reviewkit_fpln_mc_inner_left .reviewkit_star_rating label {
  font-size: 26px;
  color: #00b67a;
}
.reviewkit_fpln_mcs .reviewkit_fpln_mc_inner_right .review_us_one {
  font-weight: 400;
}
.reviewkit_fpln_mcs .reviewkit_fpln_mc_inner_right .reviewkit_fpln_star_icon {
  margin-right: 0;
}

.reviewkit_fpln_mcts {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 10px;
}
.reviewkit_fpln_mcts .reviewkit_fpln_inner_left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 10px;
}
.reviewkit_fpln_mcts .reviewkit_fpln_inner_left .reviewkit_out_of {
  font-weight: 400;
  word-spacing: 1px;
}
.reviewkit_fpln_mcts .reviewkit_fpln_inner_right .reviewkit_fpln_star_icon {
  margin-right: 0;
}

.reviewkit_fpln_mini {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
.reviewkit_fpln_mini .reviewkit_fpln_inner_top {
  margin-bottom: 8px;
}
.reviewkit_fpln_mini .reviewkit_fpln_inner_top .reviewkit_fpln_star_icon {
  margin-right: 0;
}
.reviewkit_fpln_mini .reviewkit_bg img {
  width: 150px;
  margin-bottom: 4px;
}
.reviewkit_fpln_mini .reviewkit_fpln_inner_bottom {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 20px;
}
.reviewkit_fpln_mini .reviewkit_fpln_inner_bottom .reviewkit_left_reviews {
  position: relative;
}
.reviewkit_fpln_mini .reviewkit_fpln_inner_bottom .reviewkit_left_reviews::before {
  position: absolute;
  content: "";
  top: 50%;
  right: -10px;
  height: 14px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 1px;
  background: #14223b;
}
.reviewkit_fpln_mini .reviewkit_fpln_inner_bottom .reviewkit_left_reviews .reviewkit_orignal_rcount {
  font-weight: 600;
}
.reviewkit_fpln_mini .reviewkit_fpln_inner_bottom .reviewkit_review_area .reviewkit_out_of {
  font-weight: 600;
}
.reviewkit_fpln_starter {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.reviewkit_fpln_starter .reviewkit_fpln_inner_top {
  margin-bottom: 6px;
}
.reviewkit_fpln_starter .reviewkit_fpln_inner_top .mirc_r_count {
  font-weight: 600;
}
.reviewkit_fpln_starter .reviewkit_bg img {
  width: 150px;
  margin-bottom: 4px;
}
.reviewkit_fpln_starter .reviewkit_toltip_wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container {
  position: relative;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 8px;
  border-radius: 6px;
  cursor: pointer;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container:hover {
  -webkit-transform: translateY(-2px);
          transform: translateY(-2px);
}
.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container .reviewkit_star {
  width: 20px;
  height: 20px;
  background-color: #00b67a;
  clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
  -webkit-transition: -webkit-transform 0.2s ease;
  transition: -webkit-transform 0.2s ease;
  transition: transform 0.2s ease;
  transition: transform 0.2s ease, -webkit-transform 0.2s ease;
}
.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container .reviewkit_text {
  font-size: 18px;
  font-weight: 600;
  color: #14223b;
  letter-spacing: -0.5px;
}
.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container:hover .reviewkit_star {
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
}
.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container .reviewkit_tooltip {
  position: absolute;
  top: 100%;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  background: #14223b;
  color: #fff;
  padding: 16px 20px;
  border-radius: 8px;
  font-size: 14px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  z-index: 10;
  margin-bottom: 0;
  -webkit-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  text-align: center;
}
.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container .reviewkit_tooltip::after {
  content: "";
  position: absolute;
  top: -16px;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  border: 8px solid transparent;
  border-bottom-color: #14223b;
}
.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container .reviewkit_tooltip .reviewkit_tooltip_content {
  margin-bottom: 6px;
}
.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container:hover .reviewkit_tooltip {
  opacity: 1;
  visibility: visible;
  -webkit-transform: translateX(-50%) translateY(7px);
          transform: translateX(-50%) translateY(7px);
}
.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container .reviewkit_tooltip_link {
  color: #00b67a;
  text-decoration: none;
  font-weight: 500;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
}
.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container .reviewkit_tooltip_link:hover {
  text-decoration: underline;
}

.reviewkit_header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}
.reviewkit_header img {
  width: 150px;
}
.reviewkit_header .reviewkit_profile_name {
  margin: 0;
}

.reviewkit_profile_review {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 5px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 15px;
}
.reviewkit_profile_review .reviewkit_review_secore {
  font-weight: 700;
}
.reviewkit_profile_review .reviewkit_review_img img {
  width: 150px;
}

.reviewkit_prium_grid_layout {
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: 1fr 20px 1fr 20px 1fr;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.reviewkit_prium_grid__single_item {
  padding: 15px;
  border-radius: 6px;
  background: #fafafa;
}
.reviewkit_prium_grid__single_item .reviewkit_ratting_author_time_wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.reviewkit_prium_grid__single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_author_left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 6px;
  margin-bottom: 15px;
}
.reviewkit_prium_grid__single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_author_left .reviewkit_star_rating {
  margin-bottom: 0;
}
.reviewkit_prium_grid__single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_author_left .reviewkit_star_rating label {
  font-size: 30px;
}
.reviewkit_prium_grid__single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_author_left .reviewkit_ratting_author {
  color: #98999a;
}
.reviewkit_prium_grid__single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_author_left .reviewkit_ratting_author span {
  color: #484849;
}
.reviewkit_prium_grid__single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_time_right P {
  margin: 0;
  color: #98999a;
}
.reviewkit_prium_grid__single_item .reviewkit_title {
  font-size: 20px;
  font-weight: 600;
  line-height: 1;
  margin: 0 0 15px;
}
.reviewkit_prium_grid__single_item .reviewkit_pra {
  font-size: 16px;
  line-height: 24px;
  margin: 0;
}

.reviewkit_prium_header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 10px;
  margin-bottom: 30px;
}
.reviewkit_prium_header .reviewkit_prim_top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 20px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
@media screen and (max-width: 450px) {
  .reviewkit_prium_header .reviewkit_prim_top {
    gap: 10px;
  }
}
.reviewkit_prium_header .reviewkit_prim_top .reviewkit_profile_name {
  margin: 0;
}
.reviewkit_prium_header .reviewkit_prim_top .reviewkit_rating_bg {
  width: 150px;
}
.reviewkit_prium_header .reviewkit_prim_bottom .reviewkit_prim_r_count {
  font-weight: 600;
}
.reviewkit_prium_header .reviewkit_prim_bottom .place_name {
  font-weight: 600;
}

.reviewkit_prium_list_layout .reviewkit_prium_list_single_item {
  padding: 15px;
  border-radius: 6px;
  background-color: #fafafa;
  margin-bottom: 30px;
}
.reviewkit_prium_list_layout .reviewkit_prium_list_single_item .reviewkit_ratting_author_time_wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.reviewkit_prium_list_layout .reviewkit_prium_list_single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_author_left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 10px;
}
.reviewkit_prium_list_layout .reviewkit_prium_list_single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_author_left .reviewkit_star_rating {
  margin-bottom: 0;
}
.reviewkit_prium_list_layout .reviewkit_prium_list_single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_author_left .reviewkit_star_rating label {
  font-size: 30px;
}
.reviewkit_prium_list_layout .reviewkit_prium_list_single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_time_right P {
  margin: 0;
  color: #98999a;
}
.reviewkit_prium_list_layout .reviewkit_prium_list_single_item .reviewkit_title {
  font-size: 20px;
  font-weight: 600;
  line-height: 1;
  margin: 0 0 15px;
}
.reviewkit_prium_list_layout .reviewkit_prium_list_single_item .reviewkit_pra {
  font-size: 16px;
  line-height: 24px;
  margin: 0;
}