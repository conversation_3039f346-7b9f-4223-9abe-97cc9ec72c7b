<?php

namespace ElegantBlocks\TrustpilotReviewskit\Admin;

use ElegantBlocks\TrustpilotReviewskit\Shortcodes;

class Assets {
	  private $pages = ['toplevel_page_elegantblocks/trustpilotreviewskit'];
    public function __construct() {
        add_action('admin_enqueue_scripts', [$this, 'plugin_scripts']);
        add_action( 'wp_enqueue_scripts', [ $this, 'enqueue_frontend_assets' ] );
    }

    public function plugin_scripts($hook) {
        if( !in_array($hook, $this->pages) ){
            return;
        }
        $dependencies = include_once TRUSTPILOT_REVIEWSKIT_ASSETS_DIR_PATH . 'js/trustpilot-reviewskit.core.min.asset.php';
        wp_enqueue_style('trustpilot-reviewskit-admin', TRUSTPILOT_REVIEWSKIT_ASSETS_URI . 'js/trustpilot-reviewskit.core.min.css', ['wp-components'], $dependencies['version'], 'all');
        wp_enqueue_script(
            'trustpilot-reviewskit',
            TRUSTPILOT_REVIEWSKIT_ASSETS_URI . 'js/trustpilot-reviewskit.core.min.js',
            array_merge($dependencies['dependencies']),
            $dependencies['version'],
            true
        );

        wp_enqueue_style(
          'trustpilot-reviewkit-shortcodes',
          TRUSTPILOT_REVIEWSKIT_ASSETS_URI . 'js/shortcode/index.min.css',
          [],
          '1.0.0'
        );

        $cache_data = $this->get_cache_data();

        wp_localize_script('trustpilot-reviewskit', 'trustpilot_reviewkit', [
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('trustpilot_reviewkit_nonce'),
            'plugin_root_url' => TRUSTPILOT_REVIEWSKIT_PLUGIN_ROOT_URI,
            'data' => $cache_data['data'] ?? [],
            'count' => $cache_data['count'] ?? 0,
            'last_updated' => $cache_data['last_updated'] ?? null,
            'original_domain' => $cache_data['original_domain'] ?? '',
            'domain' => $cache_data['domain'] ?? '',
        ]);
    }

    /**
	 * Enqueue frontend assets for shortcodes
	 */
	public function enqueue_frontend_assets() {
		if( !file_exists( TRUSTPILOT_REVIEWSKIT_ASSETS_DIR_PATH . 'js/shortcode/index.min.css' ) ) {
			return;
		}
		if ( ! Shortcodes::has_shortcodes_on_page() ) {
			return;
		}
		wp_enqueue_style(
			'trustpilot-reviewkit-shortcodes',
			TRUSTPILOT_REVIEWSKIT_ASSETS_URI . 'js/shortcode/index.min.css',
			[],
			time()
		);
	}


    public function get_cache_data(){
      $data_map = [
      	'original_domain' => 'reviewkit_tp_reviews_original_domain',
        'domain' => 'reviewkit_tp_reviews_domain',
        'data' => 'reviewkit_tp_reviews_data',
        'misc_data' => 'reviewkit_tp_reviews_misc_data',
        'business_data' => 'reviewkit_tp_reviews_business_data',
        'count' => 'reviewkit_tp_reviews_count',
        'last_updated' => 'reviewkit_tp_reviews_last_updated'
      ];

      $results = [];
      foreach ($data_map as $key => $option_name) {
        $option_value = get_option($option_name, null);
        if ($option_value !== null) {
          $results[] = [
            'option_name' => $option_name,
            'option_value' => $option_value
          ];
        }
      }

      $data = [];

      foreach($results as $result){
           foreach($data_map as $key => $prefix) {
               if(substr($result['option_name'], 0, strlen($prefix)) === $prefix) {
                   if($key === 'data' || $key === 'misc_data' || $key === 'business_data') {
                   		$unserialize_data = maybe_unserialize( $result['option_value'] );
                      $data[$key] = $unserialize_data;
                   } else {
                      $data[$key] = $result['option_value'];
                   }
               }
           }
      }

      // Reconstruct the complete API response for frontend compatibility
      if (isset($data['misc_data']) && isset($data['business_data']) && isset($data['data'])) {
          $complete_data = $data['misc_data'];
          $complete_data['business_details'] = $data['business_data'];
          $complete_data['reviews'] = $data['data'];
          $data['data'] = $complete_data;
      }

       return $data;

    }
}
