<?php

namespace ElegantBlocks\TrustpilotReviewskit\Shortcodes;

use ElegantBlocks\TrustpilotReviewskit\BusinessData;

/**
 * Micro TrustScore shortcode
 */
class MicroTrustScore extends BaseShortcode {

	/**
	 * Constructor
	 */
	public function __construct() {
		$this->tag = 'reviewkit_trustpilot_micro_trustscore';

		// Get dynamic business data
		$business_data = BusinessData::get_business_data();

		$this->default_attributes = [
			'rating' => BusinessData::get_rating_text( $business_data['trustScore'] ?? $business_data['trust_score'] ?? 4.8 ),
			'score'  => $business_data['trustScore'] ?? $business_data['trust_score'] ?? 4.8,
			'url'    => $business_data['trustpilot_url'] ?? '#',
		];
	}

	/**
	 * Generate the HTML output
	 *
	 * @param array $atts Sanitized attributes
	 * @param string $content Shortcode content
	 * @return string
	 */
	protected function generate_html( $atts, $content ) {
		$rating = $this->default_attributes['rating'];
		$score = $this->default_attributes['score'];
		$single_star = BusinessData::get_single_star_image_url();

		$html = '<div class="reviewkit_fpln_mcts reviewkit_fpln_common">';
		
		// Left section with rating and score
		$html .= '<div class="reviewkit_fpln_inner_left">';
		$html .= '<span class="review_us_one">' . esc_html( $rating ) . '</span>';
		
		$html .= '<div class="reviewkit_score">';
		$html .= '<span class="reviewkit_orignal_rcount">' . esc_html( $score ) . '</span>';
		$html .= '<span class="reviewkit_out_of"> out of 5</span>';
		$html .= '</div>';
		
		$html .= '</div>';
		
		// Right section with Trustpilot logo
		$html .= '<div class="reviewkit_fpln_inner_right">';
		$html .= '<img src="' . esc_url( $single_star ) . '" alt="Trustpilot logo"  style="width: 20px;" />';
		$html .= '<span class="place_name"> Trustpilot</span>';
		$html .= '</div>';
		
		$html .= '</div>';

		return $html;
	}
}
