<?php

namespace ElegantBlocks\TrustpilotReviewskit\Shortcodes;

use ElegantBlocks\TrustpilotReviewskit\BusinessData;

/**
 * Mini Trustbox shortcode
 */
class MiniTrustbox extends BaseShortcode {

	/**
	 * Constructor
	 */
	public function __construct() {
		$this->tag = 'reviewkit_trustpilot_mini';

		// Get dynamic business data
		$business_data = BusinessData::get_business_data();

		$this->default_attributes = [
			'trustscore' => $business_data['trust_score'],
			'reviews'    => $business_data['review_count'],
			'url'        => $business_data['trustpilot_url'],
		];
	}

	/**
	 * Generate the HTML output
	 *
	 * @param array $atts Sanitized attributes
	 * @param string $content Shortcode content
	 * @return string
	 */
	protected function generate_html( $atts, $content ) {
		$business_data = new BusinessData();
		$trustscore = $business_data->profile['trustScore'];
		$reviews = $business_data->profile['numberOfReviews'];

		$url = $business_data->profile['websiteUrl'];
		$single_star = BusinessData::get_single_star_image_url();

		// Debug: Add a simple test output first
		$html = '<!-- Trustpilot Mini Shortcode Debug: Working -->';
		$html .= '<div class="reviewkit_fpln_mini">';
		
		// Top section with Trustpilot logo
		$html .= '<div class="reviewkit_fpln_inner_top">';
		// $html .= $this->get_star_svg();
		$html .= '<img src="' . esc_url( $single_star ) . '" alt="Trustpilot logo" />';
		$html .= '<span class="place_name">Trustpilot</span>';
		$html .= '</div>';
		
		// Star rating image - dynamic based on trust score
		$html .= '<div class="reviewkit_bg">';
		$star_image_url = BusinessData::get_star_image_url( $trustscore );
		$rating_text = BusinessData::get_rating_text( $trustscore );
		$html .= '<img src="' . esc_url( $star_image_url ) . '" alt="' . esc_attr( $rating_text . ' rating' ) . '">';
		$html .= '</div>';
		
		// Bottom section with score and reviews
		$html .= '<div class="reviewkit_fpln_inner_bottom">';
		
		// Left side - TrustScore
		$html .= '<div class="reviewkit_left_reviews">';
		$html .= '<span class="review_us_one">TrustScore </span>';
		$html .= '<span class="reviewkit_orignal_rcount">' . esc_html( $trustscore ) . '</span>';
		$html .= '</div>';
		
		// Right side - Review count
		$html .= '<div class="reviewkit_review_area">';
		$formatted_count = BusinessData::format_review_count( $reviews );
		$html .= '<span class="reviewkit_out_of">' . esc_html( $formatted_count ) . '</span>';
		$html .= '<span class="reviewkit_reviews"> reviews</span>';
		$html .= '</div>';
		
		$html .= '</div>'; // Close inner_bottom
		$html .= '</div>'; // Close main container

		return $html;
	}
}
