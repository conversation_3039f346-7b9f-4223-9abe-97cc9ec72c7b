<?php

namespace ElegantBlocks\TrustpilotReviewskit\Shortcodes;

use ElegantBlocks\TrustpilotReviewskit\BusinessData;

/**
 * Micro Button shortcode
 */
class MicroButton extends BaseShortcode {

	/**
	 * Constructor
	 */
	public function __construct() {
		$this->tag = 'reviewkit_trustpilot_micro_button';

		// Get dynamic business data
		$business_data = BusinessData::get_business_data();

		$this->default_attributes = [
			'reviews' => BusinessData::format_review_count( $business_data['numberOfReviews'] ?? $business_data['review_count'] ?? 347 ),
			'url'     => $business_data['trustpilot_url'] ?? '#',
		];
	}

	/**
	 * Generate the HTML output
	 *
	 * @param array $atts Sanitized attributes
	 * @param string $content Shortcode content
	 * @return string
	 */
	protected function generate_html( $atts, $content ) {
		$reviews = $this->default_attributes['reviews'];
		$url = $this->default_attributes['url'];

		$html = '<div class="reviewkit_fpln_mcb_wrap">';

		$light_star = TRUSTPILOT_REVIEWSKIT_ASSETS_URI . "images/light-single-star.png";
		
		// Left button with Trustpilot branding
		$html .= '<a class="reviewkit_fpln_mcb_left" href="' . esc_url( $url ) . '">';
		$html .= '<img src="' . esc_url( $light_star ) . '" alt="Trustpilot logo"  style="width: 20px;" />';
		$html .= '<span class="place_name">Trustpilot</span>';
		$html .= '</a>';
		
		// Right section with review count
		$html .= '<div class="reviewkit_fpln_mcb_right">';
		$html .= esc_html( $reviews ) . ' reviews';
		$html .= '</div>';
		
		$html .= '</div>';

		return $html;
	}
}
