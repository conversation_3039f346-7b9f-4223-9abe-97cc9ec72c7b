<?php

namespace ElegantBlocks\TrustpilotReviewskit;

use ElegantBlocks\TrustpilotReviewskit\Shortcodes\MiniTrustbox;
use ElegantBlocks\TrustpilotReviewskit\Shortcodes\StarterTrustbox;
use ElegantBlocks\TrustpilotReviewskit\Shortcodes\MicroTrustScore;
use ElegantBlocks\TrustpilotReviewskit\Shortcodes\MicroStar;
use ElegantBlocks\TrustpilotReviewskit\Shortcodes\MicroReviewCount;
use ElegantBlocks\TrustpilotReviewskit\Shortcodes\MicroButton;
use ElegantBlocks\TrustpilotReviewskit\Shortcodes\MicroCombo;

/**
 * Main Shortcodes class to register and manage all Trustpilot shortcodes
 */
class Shortcodes {

	/**
	 * Array of shortcode instances
	 *
	 * @var array
	 */
	public $shortcodes = [];

	/**
	 * Constructor
	 */
	public function __construct() {
		$this->init_shortcodes();
}

	/**
	 * Initialize all shortcodes
	 */
	public function init_shortcodes() {
		$this->shortcodes = [
			'mini'             => new MiniTrustbox(),
			'starter'          => new StarterTrustbox(),
			'micro_trustscore' => new MicroTrustScore(),
			'micro_star'       => new MicroStar(),
			'micro_reviewcount' => new MicroReviewCount(),
			'micro_button'     => new MicroButton(),
			'micro_combo'      => new MicroCombo(),
		];

		// Register each shortcode
		foreach ( $this->shortcodes as $key => $shortcode ) {
			$shortcode->register();
		}
	}

	public function enqueue_shortcode_assets() {

	}

	/**
	 * Check if any of our shortcodes are being used on the current page
	 *
	 * @return bool
	 */
	public static function has_shortcodes_on_page() {
		global $post;

		if ( ! $post ) {
			return false;
		}

		$shortcode_tags = [
			'reviewkit_trustpilot_mini',
			'reviewkit_trustpilot_starter',
			'reviewkit_trustpilot_micro_trustscore',
			'reviewkit_trustpilot_micro_star',
			'reviewkit_trustpilot_micro_reviewcount',
			'reviewkit_trustpilot_micro_button',
			'reviewkit_trustpilot_micro_combo',
		];

		foreach ( $shortcode_tags as $tag ) {
			if ( has_shortcode( $post->post_content, $tag ) ) {
				return true;
			}
		}

		return false;
	}

	/**
	 * Get all registered shortcodes
	 *
	 * @return array
	 */
	public function get_shortcodes() {
		return $this->shortcodes;
	}

	/**
	 * Get shortcode information for admin interface
	 *
	 * @return array
	 */
	public function get_shortcodes_info() {
		return [
			[
				'name'        => 'Mini Trustbox',
				'shortcode'   => '[reviewkit_trustpilot_mini]',
				'description' => 'Compact widget showing TrustScore, star rating, and review count',
				'category'    => 'free',
			],
			[
				'name'        => 'Starter Trustbox',
				'shortcode'   => '[reviewkit_trustpilot_starter]',
				'description' => 'Interactive widget with star rating and tooltip',
				'category'    => 'free',
			],
			[
				'name'        => 'Micro TrustScore',
				'shortcode'   => '[reviewkit_trustpilot_micro_trustscore]',
				'description' => 'Simple display of TrustScore rating',
				'category'    => 'free',
			],
			[
				'name'        => 'Micro Star',
				'shortcode'   => '[reviewkit_trustpilot_micro_star]',
				'description' => 'Interactive star rating display',
				'category'    => 'free',
			],
			[
				'name'        => 'Micro Review Count',
				'shortcode'   => '[reviewkit_trustpilot_micro_reviewcount]',
				'description' => 'Link showing review count',
				'category'    => 'free',
			],
			[
				'name'        => 'Micro Button',
				'shortcode'   => '[reviewkit_trustpilot_micro_button]',
				'description' => 'Button-style widget with review count',
				'category'    => 'free',
			],
			[
				'name'        => 'Micro Combo',
				'shortcode'   => '[reviewkit_trustpilot_micro_combo]',
				'description' => 'Combined star rating and review count widget',
				'category'    => 'free',
			],
		];
	}
}
