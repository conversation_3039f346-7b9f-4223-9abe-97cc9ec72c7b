<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit609dae41b31abbae3b15af5404887df5
{
    public static $prefixLengthsPsr4 = array (
        'E' => 
        array (
            'ElegantBlocks\\TrustpilotReviewskit\\' => 35,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'ElegantBlocks\\TrustpilotReviewskit\\' => 
        array (
            0 => __DIR__ . '/../..' . '/includes',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit609dae41b31abbae3b15af5404887df5::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit609dae41b31abbae3b15af5404887df5::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit609dae41b31abbae3b15af5404887df5::$classMap;

        }, null, ClassLoader::class);
    }
}
