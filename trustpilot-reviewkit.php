<?php
/**
 * Plugin Name:       Trustpilot Reviewkit
 * Description:       A Better Plugin for Managing your Trustpilot Reviews
 * Version:           0.1.0
 * Requires at least: 6.7
 * Requires PHP:      7.4
 * Author:            Elegant Blocks
 * License:           GPL-2.0-or-later
 * License URI:       https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain:       trustpilot-reviewkit
 *
 * @package CreateBlock
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

if ( file_exists( __DIR__ . '/vendor/autoload.php' ) ) {
	require_once __DIR__ . '/vendor/autoload.php';
}

if ( ! class_exists( 'TrustpilotReviewkit' ) ) {
	final class TrustpilotReviewkit {

		private function __construct() {
			$this->define_constants();
			register_activation_hook( __FILE__, array( $this, 'activate' ) );
			register_deactivation_hook( __FILE__, array( $this, 'deactivate' ) );
			add_action( 'init', array( $this, 'on_plugins_loaded' ) );
			add_action( 'main_plugin_loaded', array( $this, 'init_plugin' ) );
		}

		public static function init() {
			static $instance = false;

			if ( ! $instance ) {
				$instance = new self();
			}

			return $instance;
		}
		public function define_constants() {
			/**
			 * Defines CONSTANTS for Whole plugins.
			 */
			define( 'TRUSTPILOT_REVIEWSKIT_SLUG', 'trustpilot-reviewskit' );
			define( 'TRUSTPILOT_REVIEWSKIT_PLUGIN_ROOT_URI', plugins_url( '/', __FILE__ ) );
			define( 'TRUSTPILOT_REVIEWSKIT_ROOT_DIR_PATH', plugin_dir_path( __FILE__ ) );
			define( 'TRUSTPILOT_REVIEWSKIT_ASSETS_DIR_PATH', TRUSTPILOT_REVIEWSKIT_ROOT_DIR_PATH . 'assets/' );
			define( 'TRUSTPILOT_REVIEWSKIT_ASSETS_URI', TRUSTPILOT_REVIEWSKIT_PLUGIN_ROOT_URI . 'assets/' );
		}


		public function on_plugins_loaded() {
			do_action( 'main_plugin_loaded' );
		}

		/**
		 * Initialize the plugin
		 *
		 * @return void
		 */
		public function init_plugin() {
			if ( is_admin() ) {
				new ElegantBlocks\TrustpilotReviewskit\Admin();
			}

			new ElegantBlocks\TrustpilotReviewskit\Admin\Assets();

			// Migrate from old domain-specific format if needed
			ElegantBlocks\TrustpilotReviewskit\BusinessData::migrate_from_domain_specific();

			// Initialize business data
			ElegantBlocks\TrustpilotReviewskit\BusinessData::init_default_data();

			// Initialize shortcodes
			new ElegantBlocks\TrustpilotReviewskit\Shortcodes();
		}


		public function load_textdomain() {
			// load_plugin_textdomain('betterlinks', false, dirname(plugin_basename(__FILE__)) . '/languages/');
		}


		public function activate() {}

		public function deactivate() {}
	}
}

/**
 * Initializes the main plugin
 *
 * @return \TrustpilotReviewkit
 */
if ( ! function_exists( 'TrustpilotReviewkit_Start' ) ) {
	function TrustpilotReviewkit_Start() {
		return TrustpilotReviewkit::init();
	}
}

// Plugin Start
TrustpilotReviewkit_Start();
