// Trustpilot Business Page Component Styles
.trustpilot-business-page {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  margin-top: 20px;

  .business-placeholder {
    text-align: center;
    padding: 40px 20px;
    color: #666;

    p {
      margin: 0;
      font-style: italic;
      font-size: 22px;
    }
  }

  .business-header {
    display: flex;
    gap: 20px;
    align-items: flex-start;

    .business-logo {
      position: relative;
      width: 122px;
      flex-shrink: 0;
      border-radius: 8px;
      overflow: hidden;
      background-color: #f8f9fa;
      padding: 8px;
      box-shadow: rgba(0, 0, 0, 0.02) 0px 1px 3px 0px, rgba(27, 31, 35, 0.15) 0px 0px 0px 1px;

      .business-image {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .business-initials {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #ff6d00, #ff8f00);
        color: white;
        font-size: 28px;
        font-weight: 700;
      }
    }

    .business-info {
      flex: 1;
      min-width: 0;

      .business-name-section {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;
        flex-wrap: wrap;

        .business-name {
          font-size: 24px;
          font-weight: 700;
          color: #333;
          margin: 0;
          line-height: 1.2;
        }

        .business-claimed,
        .business-verified {
          background: #28a745;
          color: white;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 11px;
          font-weight: 600;
          white-space: nowrap;
        }

        .business-verified {
          background: #007bff;
        }
      }

      .business-website {
        color: #666;
        margin: 0 0 4px 0;
        font-size: 14px;
      }

      .business-category {
        color: #888;
        margin: 0 0 16px 0;
        font-size: 13px;
        font-style: italic;
      }

      .business-rating {
        margin-bottom: 16px;

        .rating-percentage{
          display: flex;
          align-items: center;
          column-gap: 10px;
          margin-bottom: 10px;

          .dynamic_stars{
            width: 200px;
            line-height: 0;

            img {
              width: 100%;
            }
          }

          > span {
            font-size: 22px;
            font-weight: 600;
          }
        }

        .review-count {
          margin-left: 8px;
          color: #666;
          font-size: 14px;
        }
      }

      .business-links {
        .business-website-link {
          // display: inline-flex;
          // align-items: center;
          // padding: 8px 16px;
          background: #007bff;
          color: white;
          // text-decoration: none;
          border-radius: 6px;
          // font-size: 14px;
          // font-weight: 500;
          transition: background-color 0.2s ease;

          &:hover {
            background: #0056b3;
            color: white;
          }

          &::after {
            content: " ↗";
            margin-left: 4px;
            font-size: 12px;
          }
        }
      }
    }
  }

  .business-stats {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 24px;
    flex-wrap: wrap;

    .stat-item {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .stat-label {
        font-size: 12px;
        color: #666;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .stat-value {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 20px;

    .business-header {
      flex-direction: column;
      gap: 16px;
      text-align: center;

      .business-logo {
        align-self: center;
      }

      .business-info {
        .business-name-section {
          justify-content: center;

          .business-name {
            font-size: 20px;
          }
        }
      }
    }

    .business-stats {
      justify-content: center;
      gap: 16px;

      .stat-item {
        text-align: center;
      }
    }
  }
}
