// Customer Info Component Styles
.customer-info {
  display: flex;
  align-items: center;
  gap: 12px;

  .customer-avatar {
    position: relative;
    border-radius: 50%;
    overflow: hidden;
    background-color: #f5f5f5;
    flex-shrink: 0;
    box-shadow: rgba(0, 0, 0, 0.02) 0px 1px 3px 0px, rgba(27, 31, 35, 0.15) 0px 0px 0px 1px;

    .customer-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 50%;
    }

    .customer-initials {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #007bff, #0056b3);
      color: white;
      font-weight: 600;
      border-radius: 50%;
    }
  }

  .customer-details {
    flex: 1;
    min-width: 0; // Allow text truncation

    .customer-name {
      font-weight: 500;
      color: #333;
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  // Size variants
  &.customer-info--small {
    gap: 8px;

    .customer-avatar {
      width: 32px;
      height: 32px;

      .customer-initials {
        font-size: 12px;
      }
    }

    .customer-details {
      .customer-name {
        font-size: 14px;
      }
    }
  }

  &.customer-info--medium {
    gap: 12px;

    .customer-avatar {
      width: 48px;
      height: 48px;

      .customer-initials {
        font-size: 16px;
      }
    }

    .customer-details {
      .customer-name {
        font-size: 16px;
      }
    }
  }

  &.customer-info--large {
    gap: 16px;

    .customer-avatar {
      width: 64px;
      height: 64px;

      .customer-initials {
        font-size: 20px;
      }
    }

    .customer-details {
      .customer-name {
        font-size: 18px;
        font-weight: 600;
      }
    }
  }
}
