// Trustpilot Star Rating Component Styles - Authentic Design
.trustpilot-star-rating {
  display: flex;
  align-items: center;
  gap: 8px;

  .stars {
    display: flex;
    align-items: center;
    gap: 3px; // Trustpilot uses tighter spacing
  }

  .star {
    display: inline-block;
    line-height: 1;
    transition: all 0.3s ease;
    user-select: none;

    &.star-full {
      // Color is set inline based on rating
    }

    &.star-half {
      position: relative;

      &::after {
        content: "★";
        position: absolute;
        left: 50%;
        top: 0;
        color: #ddd;
        z-index: -1;
      }
    }

    &.star-empty {
      color: #ddd;
    }

    // Hover effect for interactive feel
    &:hover {
      transform: scale(1.05);
    }
  }

  // SVG Star styles for authentic Trustpilot look
  .star-svg {
    display: inline-block;
    transition: all 0.3s ease;

    &.star-filled {
      // Fill color is set inline
    }

    &.star-empty {
      fill: #ddd;
    }

    // Hover effect
    &:hover {
      transform: scale(1.05);
    }
  }

  .star-svg-half {
    display: inline-block;
    position: relative;
  }

  .rating-value {
    font-size: 0.9em;
    color: #666;
    font-weight: 500;
    margin-left: 4px;
  }

  // Size variants matching Trustpilot patterns
  &.trustpilot-star-rating--small {
    gap: 6px;

    .stars {
      gap: 2px;
    }

    .star {
      font-size: 14px;
    }

    .star-svg {
      width: 14px;
      height: 14px;
    }

    .rating-value {
      font-size: 12px;
    }
  }

  &.trustpilot-star-rating--medium {
    gap: 8px;

    .stars {
      gap: 3px;
    }

    .star {
      font-size: 18px;
    }

    .star-svg {
      width: 18px;
      height: 18px;
    }

    .rating-value {
      font-size: 14px;
    }
  }

  &.trustpilot-star-rating--large {
    gap: 10px;

    .stars {
      gap: 4px;
    }

    .star {
      font-size: 26px; // Matches Trustpilot's microstar size
    }

    .star-svg {
      width: 26px;
      height: 26px;
    }

    .rating-value {
      font-size: 16px;
      font-weight: 600;
    }
  }
}

// Legacy support - keep old class name working
.star-rating {
  @extend .trustpilot-star-rating;
}
