@use "variables";


.reviewkit_fpln_mini {
    &.preview {
        .reviewkit_fpln_inner_top{
            .place_name {
                font-size: 24px;
            }
        }
    }
}

.reviewkit_fpln_mini {
    width: 300px;
    display: inline-flex;
    // flex-wrap: wrap;
    flex-direction: column;

    .reviewkit_fpln_inner_top {
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        column-gap: 3px;

        .reviewkit_fpln_star_icon {
            margin-right: 0;
        }

        > span {
            font-weight: bold;
        }

        img {
            width: 30px;
        }

    }

    .reviewkit_bg {
        img {
            width: 100%;
            margin-bottom: 4px;
        }
    }

    .reviewkit_fpln_inner_bottom {
        display: flex;
        gap: 20px;
        font-size: 1.2rem;

        .reviewkit_left_reviews {
            position: relative;

            &::before {
                position: absolute;
                content: "";
                top: 50%;
                right: -10px;
                height: 14px;
                transform: translateY(-50%);
                width: 1px;
                background: variables.$body-text-color;
            }

            .review_us_one {}

            .reviewkit_orignal_rcount {
                font-weight: 600;
            }
        }

        .reviewkit_review_area {
            .reviewkit_out_of {
                font-weight: 600;
            }

            .reviewkit_reviews {}

        }
    }

}