/* @use "../../../node_modules/@picocss/pico/scss/pico.scss"; */

// Import component styles
@import "star-rating";
@import "customer-info";
@import "review-card";
@import "trustpilot-business";
@import "review-list";

#trustpilot-reviewskit-body {
  margin-right: 20px;

  nav {
    background: white;
    margin-top: 20px;
    padding: 0 20px;

    ul {
      display: flex;
      column-gap: 20px;
      padding: 20px 10px;
      align-items: center;
      margin: 0;

      li {
        color: black;
        cursor: pointer;
        font-weight: 600;
        font-size: 16px;
        margin-bottom: 0px;

        &.active {
          color: #007bff;
        }
      }
    }
  }

  .page {
    margin-top: 20px;

    &.review-page {
      > .review-fetch {
        .button-container {
          /* margin-top: 10px; */
          display: flex;
          column-gap: 10px;
          > button {
            margin-top: 10px;
          }
        }
      }

      .review-page-details {
        border-radius: 8px;
        overflow: hidden;
        background-color: #fff;
        border-radius: 5px;
        /* padding: 20px; */
        margin-top: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        height: 150px;
        display: flex;
        column-gap: 20px;

        > .image-wrapper {
          width: 150px;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 8px;

          > img {
            width: 100%;
            object-fit: contain;
          }
        }

        > .business-details {
          h3 {
            font-size: 22px;
            margin-bottom: 5px;
          }
          p {
            margin: 0;
          }
        }
      }

      .review-data {
        // @import "card.scss";
        margin-top: 20px;

        .cardlist {
          .card {
            padding: 0;
          }
          .review-header {
            display: flex;
            column-gap: 10px;
            border-bottom: 1px solid gray;
            padding: 10px;

            > .image-wrapper {
              img {
                width: 50px;
                height: 50px;
              }
            }

            > .name-wrapper {
              > * {
                margin: 0;
              }
            }
          }

          .review-content {
            padding: 10px;
          }
        }
      }
    }

    // Shortcodes page styles
    &.shortcodes-page {
      .shortcodes-header {
        margin-bottom: 30px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        h2 {
          font-size: 24px;
          margin-bottom: 10px;
        }

        p {
          color: #666;
          margin-bottom: 20px;
        }

        .shortcodes-search {
          max-width: 400px;
          > div {
            margin-bottom: 0;
          }
        }
      }

      .shortcodes-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 20px;
        margin-bottom: 30px;

        .shortcode-card {
          border: 1px solid #ddd;
          border-radius: 8px;
          transition: box-shadow 0.2s ease;

          &.micro_combo {
            width: 600px;
          }

          &:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
          }

          .components-card__header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid #f0f0f0;

            h3 {
              margin: 0;
              font-size: 18px;
              font-weight: 600;
            }

            .shortcode-category {
              background: #007bff;
              color: white;
              padding: 4px 8px;
              border-radius: 12px;
              font-size: 12px;
              text-transform: uppercase;
              font-weight: 500;
            }
          }

          .components-card__body {
            padding: 20px;

            .shortcode-preview {
              margin-bottom: 16px;

              .preview-component {
                background: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                padding: 16px;
                min-height: 80px;
                display: flex;
                align-items: center;
                justify-content: center;
                overflow: hidden;

                // Import shortcode styles for previews
                @import "../../../assets/css/shortcodes.css";
              }

              .preview-placeholder {
                background: #f8f9fa;
                border: 2px dashed #ddd;
                border-radius: 6px;
                padding: 20px;
                text-align: center;
                color: #666;

                span {
                  font-size: 24px;
                  display: block;
                  margin-bottom: 8px;
                }

                small {
                  font-size: 12px;
                  text-transform: uppercase;
                  letter-spacing: 0.5px;
                }
              }
            }

            .shortcode-description {
              color: #555;
              margin-bottom: 16px;
              line-height: 1.5;
            }

            .shortcode-code {
              display: flex;
              align-items: center;
              gap: 10px;
              margin-bottom: 16px;
              padding: 12px;
              background: #f8f9fa;
              border-radius: 6px;

              code {
                flex: 1;
                background: none;
                padding: 0;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 13px;
                color: #d63384;
              }

              .components-button {
                &.copied {
                  background: #28a745;
                  color: white;
                  border-color: #28a745;
                }
              }
            }

            .shortcode-attributes {
              summary {
                cursor: pointer;
                font-weight: 500;
                margin-bottom: 10px;
                color: #007bff;

                &:hover {
                  text-decoration: underline;
                }
              }

              .attributes-list {
                padding-left: 16px;

                .attribute-item {
                  margin-bottom: 12px;
                  padding-bottom: 8px;
                  border-bottom: 1px solid #f0f0f0;

                  &:last-child {
                    border-bottom: none;
                    margin-bottom: 0;
                  }

                  strong {
                    color: #333;
                    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                    font-size: 13px;
                  }

                  .attribute-default {
                    color: #666;
                    font-size: 12px;
                    margin-left: 8px;
                  }

                  p {
                    margin: 4px 0 0 0;
                    color: #555;
                    font-size: 13px;
                  }
                }
              }
            }
          }
        }
      }

      .no-results {
        text-align: center;
        padding: 40px 20px;
        color: #666;

        p {
          margin: 0;
          font-style: italic;
        }
      }

      // Responsive design
      @media (max-width: 768px) {
        .shortcodes-grid {
          grid-template-columns: 1fr;
          gap: 16px;
        }

        .shortcode-card {
          .components-card__header {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
          }

          .shortcode-code {
            flex-direction: column;
            align-items: stretch;

            .components-button {
              width: 100%;
            }
          }
        }
      }
    }
  }
}
