// Review List Component Styles
.review-list {
  margin-top: 20px;
  .review-list-header {
    margin-bottom: 24px;

    .review-list-title {
      font-size: 20px;
      font-weight: 600;
      color: #333;
      margin: 0 0 16px 0;
    }

    .review-filters {
      .rating-filter {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;

        .filter-btn {
          padding: 6px 12px;
          border: 1px solid #ddd;
          background: white;
          color: #666;
          border-radius: 20px;
          font-size: 13px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
          white-space: nowrap;

          &:hover {
            border-color: #007bff;
            color: #007bff;
          }

          &.active {
            background: #007bff;
            border-color: #007bff;
            color: white;
          }
        }
      }
    }
  }

  .review-list-empty {
    padding: 40px 20px;
    color: #666;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px dashed #ddd;
    margin-top: 40px;

    p {
      margin: 0;
      font-style: italic;
      font-size: 22px;
      text-align: center;
    }
  }
  .review-list-content {

    .review-cards {
      // Individual review cards are styled in _review-card.scss
    }
  }

  .review-list-controls {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;

    .load-more-btn,
    .show-less-btn {
      padding: 10px 20px;
      border: 1px solid #007bff;
      background: white;
      color: #007bff;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #007bff;
        color: white;
      }
    }

    .show-less-btn {
      border-color: #6c757d;
      color: #6c757d;

      &:hover {
        background: #6c757d;
        color: white;
      }
    }
  }

  .review-summary {
    text-align: center;
    margin-top: 16px;

    .review-count-summary {
      color: #666;
      font-size: 14px;
      margin: 0;
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    .review-list-header {
      .review-list-title {
        font-size: 18px;
      }

      .review-filters {
        .rating-filter {
          gap: 6px;

          .filter-btn {
            padding: 5px 10px;
            font-size: 12px;
          }
        }
      }
    }

    .review-list-controls {
      flex-direction: column;
      align-items: center;

      .load-more-btn,
      .show-less-btn {
        width: 100%;
        max-width: 280px;
      }
    }
  }
}
