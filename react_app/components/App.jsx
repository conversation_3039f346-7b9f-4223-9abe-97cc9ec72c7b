import React from "react";
import Layout from "./pages/Layout";
import { useDataContext } from "./context/data-context";
import ShortCodes from "./pages/shortcodes";
import Reviews from "./pages/reviews";

const App = () => {
  const { tab } = useDataContext();
  return (
    <div>
      <Layout />
      {tab === "reviews" && <Reviews />}
      {tab === "shortcodes" && <ShortCodes />}
    </div>
  );
};

export default App;
