import React from "react";

/**
 * CustomerInfo component for displaying customer information
 * @param {Object} customer - Customer object with id, name, and image
 * @param {string} customer.name - Customer name
 * @param {string} customer.image - Customer profile image URL
 * @param {string} size - Size variant: 'small', 'medium', 'large' (default: 'medium')
 * @param {boolean} showImage - Whether to show customer image (default: true)
 */
const CustomerInfo = ({ customer = {}, size = "medium", showImage = true }) => {
  const { name = "Anonymous", image = "" } = customer;

  // Generate initials from name for fallback
  const getInitials = (name) => {
    if (!name || name === "Customer") return "?";
    return name
      .split(" ")
      .map((word) => word.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className={`customer-info customer-info--${size}`}>
      {showImage && (
        <div className="customer-avatar">
          {image ? (
            <img
              src={image}
              alt={`${name}'s profile`}
              className="customer-image"
              onError={(e) => {
                e.target.style.display = "none";
                e.target.nextSibling.style.display = "flex";
              }}
            />
          ) : null}
          <div
            className="customer-initials"
            style={{ display: image ? "none" : "flex" }}
          >
            {getInitials(name)}
          </div>
        </div>
      )}
      <div className="customer-details">
        <span className="customer-name">{name}</span>
      </div>
    </div>
  );
};

export default CustomerInfo;
