import React from "react";

/**
 * StarRating component for displaying star ratings with authentic Trustpilot design
 * @param {number} rating - The rating value (0-5)
 * @param {number} maxStars - Maximum number of stars (default: 5)
 * @param {string} size - Size of stars: 'small', 'medium', 'large' (default: 'medium')
 * @param {boolean} showRating - Whether to show the numeric rating (default: false)
 * @param {boolean} useSvg - Whether to use SVG stars instead of Unicode (default: false)
 */
const StarRating = ({ rating = 0, maxStars = 5, size = "medium", showRating = false, useSvg = false }) => {
  const stars = [];
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 !== 0;

  // Get Trustpilot color based on rating
  const getTrustpilotColor = (rating) => {
    if (rating <= 1) return "#ff3722"; // Red
    if (rating <= 2) return "#ff8622"; // Orange
    if (rating <= 3) return "#ffce00"; // Yellow
    if (rating <= 4) return "#73cf11"; // Light Green
    return "#00b67a"; // Trustpilot Green
  };

  const starColor = getTrustpilotColor(rating);

  // SVG Star component for authentic Trustpilot look
  const TrustpilotSvgStar = ({ filled, color }) => (
    <svg
      className={`star-svg ${filled ? 'star-filled' : 'star-empty'}`}
      viewBox="0 0 24 24"
      style={{ fill: filled ? color : '#ddd' }}
    >
      <path d="M12 2l2.938 7.968h8.382l-6.76 4.898 2.553 7.834L12 17.334l-7.113 5.366 2.553-7.834-6.76-4.898h8.382z" />
    </svg>
  );

  // Generate full stars
  for (let i = 0; i < fullStars; i++) {
    stars.push(
      useSvg ? (
        <TrustpilotSvgStar
          key={`full-${i}`}
          filled={true}
          color={starColor}
        />
      ) : (
        <span
          key={`full-${i}`}
          className="star star-full"
          style={{ color: starColor }}
        >
          ★
        </span>
      )
    );
  }

  // Add half star if needed
  if (hasHalfStar && fullStars < maxStars) {
    stars.push(
      useSvg ? (
        <div key="half" className="star-svg-half" style={{ position: 'relative' }}>
          <TrustpilotSvgStar filled={false} color="#ddd" />
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '50%',
            overflow: 'hidden'
          }}>
            <TrustpilotSvgStar filled={true} color={starColor} />
          </div>
        </div>
      ) : (
        <span
          key="half"
          className="star star-half"
          style={{ color: starColor }}
        >
          ★
        </span>
      )
    );
  }

  // Generate empty stars
  const emptyStars = maxStars - fullStars - (hasHalfStar ? 1 : 0);
  for (let i = 0; i < emptyStars; i++) {
    stars.push(
      useSvg ? (
        <TrustpilotSvgStar
          key={`empty-${i}`}
          filled={false}
          color="#ddd"
        />
      ) : (
        <span key={`empty-${i}`} className="star star-empty">
          ★
        </span>
      )
    );
  }

  return (
    <div className={`trustpilot-star-rating trustpilot-star-rating--${size}`}>
      <div className="stars">{stars}</div>
      {showRating && <span className="rating-value">({rating})</span>}
    </div>
  );
};

export default StarRating;
