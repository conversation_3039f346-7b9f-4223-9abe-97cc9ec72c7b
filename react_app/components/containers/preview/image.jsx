import { plugin_root_url } from "../../helper/utils";

const SingleImage = ({type='', ...rest}) => {
    if( type === 'light' ) {
        return (
            <img
                src={plugin_root_url + "/assets/images/light-single-star.png"}
                alt="Trustpilot Logo"
                style={{ width: '20px' }}
            />
        )
    }

    return ( 
        <img
            src={plugin_root_url + "/assets/images/single-star.svg"}
            alt="Trustpilot Logo"
            {...rest}
        />
     );
}
 
export default SingleImage;