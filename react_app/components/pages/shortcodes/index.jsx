import React, { useState } from "react";
import {
	Button,
	TextControl,
	Card,
	CardBody,
	CardHeader,
} from "@wordpress/components";
import { __ } from "@wordpress/i18n";

// Import preview components
import MiniTrustboxPreview from "../../shortcode-previews/MiniTrustboxPreview";
import StarterTrustboxPreview from "../../shortcode-previews/StarterTrustboxPreview";
import MicroTrustScorePreview from "../../shortcode-previews/MicroTrustScorePreview";
import MicroStarPreview from "../../shortcode-previews/MicroStarPreview";
import MicroReviewCountPreview from "../../shortcode-previews/MicroReviewCountPreview";
import MicroButtonPreview from "../../shortcode-previews/MicroButtonPreview";
import MicroComboPreview from "../../shortcode-previews/MicroComboPreview";
import { plugin_root_url } from "../../helper/utils";

const ShortCodes = () => {
	const [searchTerm, setSearchTerm] = useState("");
	const [copiedShortcode, setCopiedShortcode] = useState("");

	// Shortcode data
	const shortcodes = [
		{
			id: "mini",
			name: "Mini Trustbox",
			shortcode: "[reviewkit_trustpilot_mini]",
			description:
				"Compact widget showing TrustScore, star rating, and review count",
			category: "free",
			preview: MiniTrustboxPreview,
			attributes: [
				{
					name: "trustscore",
					default: "4.8",
					description: "Trust score rating (0-5)",
				},
				{ name: "reviews", default: "347", description: "Number of reviews" },
				{ name: "url", default: "#", description: "Link URL" },
			],
		},
		{
			id: "starter",
			name: "Starter Trustbox",
			shortcode: "[reviewkit_trustpilot_starter]",
			description: "Interactive widget with star rating and tooltip",
			category: "free",
			preview: StarterTrustboxPreview,
			attributes: [
				{ name: "reviews", default: "1,376", description: "Number of reviews" },
				{ name: "url", default: "#", description: "Link URL" },
			],
		},
		{
			id: "micro_trustscore",
			name: "Micro TrustScore",
			shortcode: "[reviewkit_trustpilot_micro_trustscore]",
			description: "Simple display of TrustScore rating",
			category: "free",
			preview: MicroTrustScorePreview,
			attributes: [
				{ name: "rating", default: "Excellent", description: "Rating text" },
				{ name: "score", default: "4.8", description: "Numeric score" },
				{ name: "url", default: "#", description: "Link URL" },
			],
		},
		{
			id: "micro_star",
			name: "Micro Star",
			shortcode: "[reviewkit_trustpilot_micro_star]",
			description: "Interactive star rating display",
			category: "free",
			preview: MicroStarPreview,
			attributes: [
				{ name: "rating", default: "Excellent", description: "Rating text" },
				{ name: "stars", default: "5", description: "Number of stars (1-5)" },
				{ name: "url", default: "#", description: "Link URL" },
			],
		},
		{
			id: "micro_reviewcount",
			name: "Micro Review Count",
			shortcode: "[reviewkit_trustpilot_micro_reviewcount]",
			description: "Link showing review count",
			category: "free",
			preview: MicroReviewCountPreview,
			attributes: [
				{ name: "count", default: "437", description: "Number of reviews" },
				{ name: "url", default: "#", description: "Link URL" },
			],
		},
		{
			id: "micro_button",
			name: "Micro Button",
			shortcode: "[reviewkit_trustpilot_micro_button]",
			description: "Button-style widget with review count",
			category: "free",
			preview: MicroButtonPreview,
			attributes: [
				{
					name: "reviews",
					default: "1.3K+",
					description: "Review count display",
				},
				{ name: "url", default: "#", description: "Link URL" },
			],
		},
		{
			id: "micro_combo",
			name: "Micro Combo",
			shortcode: "[reviewkit_trustpilot_micro_combo]",
			description: "Combined star rating and review count widget",
			category: "free",
			preview: MicroComboPreview,
			attributes: [
				{ name: "rating", default: "Excellent", description: "Rating text" },
				{ name: "stars", default: "5", description: "Number of stars (1-5)" },
				{ name: "reviews", default: "437", description: "Number of reviews" },
				{ name: "url", default: "#", description: "Link URL" },
			],
		},
	];

	// Filter shortcodes based on search term
	const filteredShortcodes = shortcodes.filter(
		(shortcode) =>
			shortcode.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			shortcode.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
			shortcode.shortcode.toLowerCase().includes(searchTerm.toLowerCase())
	);

	// Copy shortcode to clipboard with fallback methods
	const copyToClipboard = async (shortcode) => {
		try {
			// Method 1: Modern Clipboard API
			if (navigator.clipboard && window.isSecureContext) {
				await navigator.clipboard.writeText(shortcode);
				setCopiedShortcode(shortcode);
				setTimeout(() => setCopiedShortcode(""), 2000);
				return;
			}

			// Method 2: Fallback using document.execCommand (deprecated but widely supported)
			const textArea = document.createElement("textarea");
			textArea.value = shortcode;
			textArea.style.position = "fixed";
			textArea.style.left = "-999999px";
			textArea.style.top = "-999999px";
			document.body.appendChild(textArea);
			textArea.focus();
			textArea.select();

			const successful = document.execCommand("copy");
			document.body.removeChild(textArea);

			if (successful) {
				setCopiedShortcode(shortcode);
				setTimeout(() => setCopiedShortcode(""), 2000);
			} else {
				throw new Error("execCommand failed");
			}
		} catch (err) {
			console.error("Failed to copy shortcode: ", err);
			// Method 3: Final fallback - show alert with shortcode
			alert(`Copy this shortcode manually: ${shortcode}`);
		}
	};

	return (
		<div className="page shortcodes-page">
			<div className="shortcodes-header" style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
				<div>
					<h2>{__("Trustpilot Shortcodes", "trustpilot-reviewkit")}</h2>
					<p>
						{__(
							"Use these shortcodes to display Trustpilot widgets on your website.",
							"trustpilot-reviewkit"
						)}
					</p>
				</div>

				<TextControl
					// label={__("Search Shortcodes", "trustpilot-reviewkit")}
					value={searchTerm}
					onChange={setSearchTerm}
					placeholder={__(
						"Search by name, description, or shortcode...",
						"trustpilot-reviewkit"
					)}
					className="shortcodes-search"
				/>
			</div>

			<div className="shortcodes-grid">
				{filteredShortcodes.map((shortcode) => (
					<Card key={shortcode.id} className={`shortcode-card ${shortcode.id}`}>
						<CardHeader>
							<h3>{shortcode.name}</h3>
							<span className="shortcode-category">{shortcode.category}</span>
						</CardHeader>
						<CardBody>
							<div className="shortcode-preview">
								{shortcode.preview ? (
									<div
										className="preview-component"
										style={{ overflow: "visible" }}
									>
										<shortcode.preview />
									</div>
								) : (
									<div className="preview-placeholder">
										<span>📊</span>
										<small>{__("Preview", "trustpilot-reviewkit")}</small>
									</div>
								)}
							</div>

							<p className="shortcode-description">{shortcode.description}</p>

							<div className="shortcode-code">
								<code>{shortcode.shortcode}</code>
								<Button
									variant="secondary"
									size="small"
									onClick={() => copyToClipboard(shortcode.shortcode)}
									className={
										copiedShortcode === shortcode.shortcode ? "copied" : ""
									}
								>
									{copiedShortcode === shortcode.shortcode
										? __("Copied!", "trustpilot-reviewkit")
										: __("Copy", "trustpilot-reviewkit")}
								</Button>
							</div>

							{/* {shortcode.attributes && shortcode.attributes.length > 0 && (
								<details className="shortcode-attributes">
									<summary>{__("Attributes", "trustpilot-reviewkit")}</summary>
									<div className="attributes-list">
										{shortcode.attributes.map((attr, index) => (
											<div key={index} className="attribute-item">
												<strong>{attr.name}</strong>
												<span className="attribute-default">
													({attr.default})
												</span>
												<p>{attr.description}</p>
											</div>
										))}
									</div>
								</details>
							)} */}
						</CardBody>
					</Card>
				))}
			</div>

			{filteredShortcodes.length === 0 && (
				<div className="no-results">
					<p>
						{__(
							"No shortcodes found matching your search.",
							"trustpilot-reviewkit"
						)}
					</p>
				</div>
			)}
		</div>
	);
};

export default ShortCodes;
