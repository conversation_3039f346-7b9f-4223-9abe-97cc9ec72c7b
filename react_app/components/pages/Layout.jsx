import React from "react";
import { useDataContext } from "../context/data-context";
const Layout = () => {
  const { tab, setTab } = useDataContext();

  return (
    <nav>
      <ul>
        {menuItems.map((item) => (
          <li
            key={item.label}
            className={`${tab === item.tab ? "active" : ""}`}
            onClick={() => setTab(item.tab)}
          >
            {item.label}
          </li>
        ))}
      </ul>
    </nav>
  );
};

export default Layout;

const menuItems = [
  { label: "Reviews", tab: "reviews" },
  { label: "Shortcodes", tab: "shortcodes" },
];
