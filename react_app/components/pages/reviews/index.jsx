import React, { useState } from "react";
import { <PERSON>Control, <PERSON><PERSON>, Spinner } from "@wordpress/components";
import { __ } from "@wordpress/i18n";
import { getReviews, original_domain } from "../../helper/utils";
import { useDataContext } from "../../context/data-context";
import TrustpilotBusinessPage from "../../containers/reviews/TrustpilotBusinessPage";
import ReviewList from "../../containers/reviews/ReviewList";

const Reviews = () => {
	const { data = {}, setData } = useDataContext();
	const { business_details = {}, reviews = [] } = data;
	const [trustpilotUrl, setTrustpilotUrl] = useState(original_domain);
	const [loading, setLoading] = useState(false);

	const __fetchReviews = async (revalidate = false) => {
		setLoading(true);
		const response = await getReviews(trustpilotUrl, { revalidate });
		if (response?.data) {
			setData(response.data);
		}
		setLoading(false);
	};

	return (
		<div className="page review-page">
			<div className="review-fetch">
				<TextControl
					__nextHasNoMarginBottom
					__next40pxDefaultSize
					label={__("Trustpilot URL", "trustpilot-reviewkit")}
					type="url"
					help={__(
						"Enter the URL of your Trustpilot page",
						"trustpilot-reviewkit"
					)}
					value={trustpilotUrl}
					onChange={(value) => setTrustpilotUrl(value)}
				/>
				<div className="button-container">
					<Button
						variant="primary"
						style={{ backgroundColor: "#28a745", fontWeight: "600" }}
						onClick={__fetchReviews}
						disabled={trustpilotUrl === ""}
					>
						{__("Fetch Reviews", "trustpilot-reviewkit")}
					</Button>
					<Button
						variant="secondary"
						onClick={() => __fetchReviews(true)}
						disabled={!data}
					>
						{__("Revalidate Reviews", "trustpilot-reviewkit")}
					</Button>
				</div>
			</div>

			{loading ? (
				<div style={{ marginTop: "20px", textAlign: "center" }}>
					<Spinner
						style={{
							height: "calc(4px * 10)",
							width: "calc(4px * 10)",
						}}
					/>
				</div>
			) : (
				<>
					<TrustpilotBusinessPage businessDetails={business_details} />
					<ReviewList reviews={reviews} />
				</>
			)}
		</div>
	);
};

export default Reviews;
