import React, { createContext, useContext, useState } from "react";
const DataContext = createContext();
import { data as reviewDetails } from "../helper/utils";

function DataContextProvider({ children }) {
  const [tab, setTab] = useState("reviews");
  const [data, setData] = useState(reviewDetails || {});

  const value = {
    tab,
    setTab,
    data,
    setData,
  };

  return <DataContext.Provider value={value}>{children}</DataContext.Provider>;
}

function useDataContext() {
  return useContext(DataContext);
}

export { DataContextProvider, useDataContext };
