import { data, formatReviewCount } from "../helper/utils";
import SingleImage from "../containers/preview/image";
import { __ } from "@wordpress/i18n";
/**
 * Micro Review Count Preview Component
 * Mirrors the exact HTML structure from Reviews-kit/microreviewcount.html
 */
const MicroReviewCountPreview = ({ count }) => {
	// Get dynamic business data
	const businessData = data?.business_details || {};
	const dynamicCount = count || businessData.numberOfReviews || 437;

	return (
		<div>
			<a className="reviewkit_fpln_mirc reviewkit_fpln_common preview" href="#">
				<span className="mirc_see">{__("See our", "reviewkit")}</span>
				<span className="mirc_r_count">{formatReviewCount(dynamicCount)}</span>
				<span className="review_us_one">{__("reviews on", "reviewkit")}</span>
				<SingleImage style={{ width: "20px" }} />
				<span className="place_name">{__("Trustpilot", "reviewkit")}</span>
			</a>
		</div>
	);
};

export default MicroReviewCountPreview;
