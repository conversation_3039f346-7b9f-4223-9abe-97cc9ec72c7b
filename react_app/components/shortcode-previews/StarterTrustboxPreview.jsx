import {
	data,
	formatReviewCount,
	getStarImageUrl,
	plugin_root_url,
} from "../helper/utils";
import { __ } from "@wordpress/i18n";
/**
 * Starter Trustbox Preview Component
 * Mirrors the exact HTML structure from Reviews-kit/starter.html
 */
const StarterTrustboxPreview = ({ reviews }) => {
	const businessData = data?.business_details || {};
	const dynamicReviews = reviews || businessData.numberOfReviews || 1376;
	const dynamicTrustScore = businessData.trustScore || 4.8;

	return (
		<div className="reviewkit_fpln_starter preview">
			<div className="reviewkit_fpln_inner_top">
				<span className="mirc_see">{__("Check out our", "reviewkit")} </span>
				<span className="mirc_r_count">
					{formatReviewCount(dynamicReviews)}
				</span>
				<span className="review_us_one"> {__("reviews", "reviewkit")}</span>
			</div>

			<div className="reviewkit_bg">
				<img
					src={getStarImageUrl(dynamicTrustScore)}
					alt={`${dynamicTrustScore} star rating`}
				/>
			</div>

			<div className="reviewkit_toltip_wrap">
				<div className="reviewkit_logo_container">
					<Tooltip />

					<img
						src={plugin_root_url + "/assets/images/single-star.svg"}
						alt="Trustpilot Logo"
					/>
					<span className="reviewkit_text">Trustpilot</span>
				</div>
			</div>
		</div>
	);
};

export default StarterTrustboxPreview;

const Tooltip = () => (
	<div className="reviewkit_tooltip reviewkit_tooltip_large">
		<div className="reviewkit_tooltip_content">
			{__("Helping each other make better choices", "reviewkit")}
		</div>
		<a href="#" className="reviewkit_tooltip_link">
			{__("Read and write reviews", "reviewkit")}
		</a>
	</div>
);
