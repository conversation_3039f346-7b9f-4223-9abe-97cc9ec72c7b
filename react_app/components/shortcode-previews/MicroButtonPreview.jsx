import { __ } from "@wordpress/i18n";
import SingleImage from "../containers/preview/image";
import { data, formatReviewCount } from "../helper/utils";

/**
 * Micro Button Preview Component
 * Mirrors the exact HTML structure from Reviews-kit/microbutton.html
 */
const MicroButtonPreview = ({ reviews }) => {
	const businessData = data?.business_details || {};
	const dynamicReviews = reviews || businessData.numberOfReviews || 1300;

	return (
		<div className="reviewkit_fpln_mcb_wrap">
			<a className="reviewkit_fpln_mcb_left" href="#">
				<SingleImage type="light" />
				<span className="place_name">{__("Trustpilot", "reviewkit")}</span>
			</a>

			<div className="reviewkit_fpln_mcb_right">
				{formatReviewCount(dynamicReviews)} {__("reviews", "reviewkit")}
			</div>
		</div>
	);
};

export default MicroButtonPreview;
