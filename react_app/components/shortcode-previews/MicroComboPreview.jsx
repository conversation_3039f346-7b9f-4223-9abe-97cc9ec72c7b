import {
	data,
	formatReviewCount,
	getRatingText,
	getStarImageUrl,
} from "../helper/utils";
import SingleImage from "../containers/preview/image";
import { __ } from "@wordpress/i18n";

/**
 * Micro Combo Preview Component
 * Mirrors the exact HTML structure from Reviews-kit/microcombo.html
 */
const MicroComboPreview = ({ rating, stars, reviews }) => {
	const businessData = data?.business_details || {};
	const dynamicScore = businessData.trustScore || 4.8;
	const dynamicReviews = reviews || businessData.numberOfReviews || 437;

	const dynamicRating = rating || getRatingText(dynamicScore);

	return (
		<div className="reviewkit_fpln_mc reviewkit_fpln_common preview">
			<div className="reviewkit_fpln_mc_inner_left">
				<span className="review_us_one">{dynamicRating}</span>
				<div className="reviewkit_star_rating">
					<img
						src={getStarImageUrl(dynamicScore)}
						alt={`${dynamicRating} star rating`}
						style={{ width: "100%" }}
					/>
				</div>
			</div>

			<div className="reviewkit_fpln_mc_inner_right">
				<span className="mirc_r_count">
					{formatReviewCount(dynamicReviews)}
				</span>
				<span className="review_us_one">{__("reviews on", "reviewkit")}</span>
				<SingleImage style={{ width: "18px" }} />
				<span className="place_name">{__("Trustpilot", "reviewkit")}</span>
			</div>
		</div>
	);
};

export default MicroComboPreview;
