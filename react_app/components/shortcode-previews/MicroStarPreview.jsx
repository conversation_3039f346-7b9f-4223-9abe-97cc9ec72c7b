import { __ } from "@wordpress/i18n";
import { data, getRatingText, getStarImageUrl } from "../helper/utils";
import SingleImage from "../containers/preview/image";

/**
 * Micro Star Preview Component
 * Mirrors the exact HTML structure from Reviews-kit/microstar.html
 */
const MicroStarPreview = ({ rating, stars }) => {
	const businessData = data?.business_details || {};
	const dynamicScore = businessData.trustScore || 4.8;

	const dynamicRating = rating || getRatingText(dynamicScore);

	return (
		<div className="reviewkit_fpln_mcs reviewkit_fpln_common preview">
			<div className="dynamic_rating">{dynamicRating}</div>
			<div className="dynamic_stars">
				<img
					src={getStarImageUrl(dynamicScore)}
					alt={`${dynamicRating} star rating`}
					style={{ width: "100%" }}
				/>
			</div>
			<div className="tp-wrapper">
				<SingleImage />
				<div className="place_name">{__("Trustpilot", "reviewkit")}</div>
			</div>
		</div>
	);

	// return (
	// 	<div className="reviewkit_fpln_mcs reviewkit_fpln_common preview">
	// 		{/* Left section with rating and stars */}
	// 		<div className="reviewkit_fpln_mc_inner_left">
	// 			<span className="review_us_one">{dynamicRating}</span>
	// 			<div className="reviewkit_star_rating">
	// 				<img
	// 					src={getStarImageUrl(dynamicScore)}
	// 					alt={`${dynamicRating} star rating`}
	// 					style={{ width: "100%" }}
	// 				/>
	// 			</div>
	// 		</div>

	// 		{/* Right section with Trustpilot logo */}
	// 		<div className="reviewkit_fpln_mc_inner_right">
	// 			<span className="review_us_one"></span>
	// 			<svg className="reviewkit_fpln_star_icon" viewBox="0 0 24 24">
	// 				<path d="M12 2l2.938 7.968h8.382l-6.76 4.898 2.553 7.834L12 17.334l-7.113 5.366 2.553-7.834-6.76-4.898h8.382z" />
	// 			</svg>
	// 			<span className="place_name">Trustpilot</span>
	// 		</div>
	// 	</div>
	// );
};

export default MicroStarPreview;
