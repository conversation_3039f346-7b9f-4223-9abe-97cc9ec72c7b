import React from "react";
import { data } from "../helper/utils";
import SingleImage from "../containers/preview/image";

/**
 * Micro TrustScore Preview Component
 * Mirrors the exact HTML structure from Reviews-kit/microtrustscore.html
 */
const MicroTrustScorePreview = ({ rating, score }) => {
	// Get dynamic business data
	const businessData = data?.business_details || {};
	const dynamicScore = score || businessData.trustScore || 4.8;

	// Get rating text based on score
	const getRatingText = (score) => {
		if (score >= 4.5) return "Excellent";
		if (score >= 4.0) return "Great";
		if (score >= 3.0) return "Good";
		if (score >= 2.0) return "Average";
		return "Poor";
	};

	const dynamicRating = rating || getRatingText(dynamicScore);
	return (
		<div className="reviewkit_fpln_mcts reviewkit_fpln_common preview">
			{/* Left section with rating and score */}
			<div className="reviewkit_fpln_inner_left">
				<span className="review_us_one">{dynamicRating}</span>

				<div className="reviewkit_score">
					<span className="reviewkit_orignal_rcount">{dynamicScore}</span>
					<span className="reviewkit_out_of"> out of 5</span>
				</div>
			</div>

			{/* Right section with Trustpilot logo */}
			<div className="reviewkit_fpln_inner_right">
				<SingleImage />
				<span className="place_name">Trustpilot</span>
			</div>
		</div>
	);
};

export default MicroTrustScorePreview;
