import SingleImage from "../containers/preview/image";
import {
	data,
	formatReviewCount,
	getStarImageUrl,
	plugin_root_url,
} from "../helper/utils";
import { __ } from "@wordpress/i18n";

/**
 * Mini Trustbox Preview Component
 * Mirrors the exact HTML structure from Reviews-kit/mini.html
 */
const MiniTrustboxPreview = ({ trustscore, reviews }) => {
	const businessData = data?.business_details || {};
	const dynamicTrustScore = trustscore || businessData.trustScore || 4.8;
	const dynamicReviews = reviews || businessData.numberOfReviews || 347;

	return (
		<div className="reviewkit_fpln_mini preview">
			<div className="reviewkit_fpln_inner_top">
				<SingleImage />
				<span className="place_name">{__("Trustpilot", "reviewkit")}</span>
			</div>

			<div className="reviewkit_bg">
				<img
					src={getStarImageUrl(dynamicTrustScore)}
					alt={`${dynamicTrustScore} star rating`}
				/>
			</div>

			{/* Bottom section with score and reviews */}
			<div className="reviewkit_fpln_inner_bottom">
				<div className="reviewkit_left_reviews">
					<span className="review_us_one">
						{__("TrustScore", "reviewkit")}{" "}
					</span>
					<span className="reviewkit_orignal_rcount">{dynamicTrustScore}</span>
				</div>

				<div className="reviewkit_review_area">
					<span className="reviewkit_out_of">
						{formatReviewCount(dynamicReviews)}
					</span>
					<span className="reviewkit_reviews">
						{" "}
						{__("reviews", "reviewkit")}
					</span>
				</div>
			</div>
		</div>
	);
};

export default MiniTrustboxPreview;
