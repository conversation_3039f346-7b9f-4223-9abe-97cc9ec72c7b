{"version": 3, "file": "shortcode.core.min.js", "mappings": ";;UAAA;UACA;;;;;WCDA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D,E;;;;;;;;;ACNA", "sources": ["webpack://trustpilot-reviewkit/webpack/bootstrap", "webpack://trustpilot-reviewkit/webpack/runtime/make namespace object", "webpack://trustpilot-reviewkit/./react_app/assets/scss/previews/main.scss?c29b"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// extracted by mini-css-extract-plugin\nexport {};"], "names": [], "sourceRoot": ""}