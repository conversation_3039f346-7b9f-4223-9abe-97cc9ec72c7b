/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./react_app/assets/scss/main.scss":
/*!*****************************************!*\
  !*** ./react_app/assets/scss/main.scss ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
// extracted by mini-css-extract-plugin


/***/ }),

/***/ "./react_app/components/App.jsx":
/*!**************************************!*\
  !*** ./react_app/components/App.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _pages_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./pages/Layout */ "./react_app/components/pages/Layout.jsx");
/* harmony import */ var _context_data_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context/data-context */ "./react_app/components/context/data-context.jsx");
/* harmony import */ var _pages_shortcodes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/shortcodes */ "./react_app/components/pages/shortcodes/index.jsx");
/* harmony import */ var _pages_reviews__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./pages/reviews */ "./react_app/components/pages/reviews/index.jsx");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react/jsx-runtime */ "react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__);






const App = () => {
  const {
    tab
  } = (0,_context_data_context__WEBPACK_IMPORTED_MODULE_2__.useDataContext)();
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxs)("div", {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_pages_Layout__WEBPACK_IMPORTED_MODULE_1__["default"], {}), tab === "reviews" && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_pages_reviews__WEBPACK_IMPORTED_MODULE_4__["default"], {}), tab === "shortcodes" && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_pages_shortcodes__WEBPACK_IMPORTED_MODULE_3__["default"], {})]
  });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (App);

/***/ }),

/***/ "./react_app/components/containers/preview/image.jsx":
/*!***********************************************************!*\
  !*** ./react_app/components/containers/preview/image.jsx ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _helper_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../helper/utils */ "./react_app/components/helper/utils.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ "react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__);


const SingleImage = ({
  type = '',
  ...rest
}) => {
  if (type === 'light') {
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("img", {
      src: _helper_utils__WEBPACK_IMPORTED_MODULE_0__.plugin_root_url + "/assets/images/light-single-star.png",
      alt: "Trustpilot Logo",
      style: {
        width: '20px'
      }
    });
  }
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("img", {
    src: _helper_utils__WEBPACK_IMPORTED_MODULE_0__.plugin_root_url + "/assets/images/single-star.svg",
    alt: "Trustpilot Logo",
    ...rest
  });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SingleImage);

/***/ }),

/***/ "./react_app/components/containers/reviews/CustomerInfo.jsx":
/*!******************************************************************!*\
  !*** ./react_app/components/containers/reviews/CustomerInfo.jsx ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ "react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__);


/**
 * CustomerInfo component for displaying customer information
 * @param {Object} customer - Customer object with id, name, and image
 * @param {string} customer.name - Customer name
 * @param {string} customer.image - Customer profile image URL
 * @param {string} size - Size variant: 'small', 'medium', 'large' (default: 'medium')
 * @param {boolean} showImage - Whether to show customer image (default: true)
 */

const CustomerInfo = ({
  customer = {},
  size = "medium",
  showImage = true
}) => {
  const {
    name = "Anonymous",
    image = ""
  } = customer;

  // Generate initials from name for fallback
  const getInitials = name => {
    if (!name || name === "Customer") return "?";
    return name.split(" ").map(word => word.charAt(0)).join("").toUpperCase().slice(0, 2);
  };
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
    className: `customer-info customer-info--${size}`,
    children: [showImage && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
      className: "customer-avatar",
      children: [image ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("img", {
        src: image,
        alt: `${name}'s profile`,
        className: "customer-image",
        onError: e => {
          e.target.style.display = "none";
          e.target.nextSibling.style.display = "flex";
        }
      }) : null, /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
        className: "customer-initials",
        style: {
          display: image ? "none" : "flex"
        },
        children: getInitials(name)
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
      className: "customer-details",
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("span", {
        className: "customer-name",
        children: name
      })
    })]
  });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CustomerInfo);

/***/ }),

/***/ "./react_app/components/containers/reviews/ReviewCard.jsx":
/*!****************************************************************!*\
  !*** ./react_app/components/containers/reviews/ReviewCard.jsx ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _StarRating__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./StarRating */ "./react_app/components/containers/reviews/StarRating.jsx");
/* harmony import */ var _CustomerInfo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CustomerInfo */ "./react_app/components/containers/reviews/CustomerInfo.jsx");
/* harmony import */ var _helper_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../helper/utils */ "./react_app/components/helper/utils.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ "react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__);





/**
 * ReviewCard component for displaying individual review
 * @param {Object} review - Review object
 * @param {string} review.reviewId - Review ID
 * @param {number} review.rating - Review rating (1-5)
 * @param {string} review.reviewTitle - Review title
 * @param {string} review.reviewBody - Review content
 * @param {Object} review.customer - Customer information
 * @param {Object} review.dates - Review dates
 * @param {string} review.reviewUrl - Review URL on Trustpilot
 */

const ReviewCard = ({
  review = {}
}) => {
  const {
    reviewId,
    rating = 0,
    reviewTitle = "",
    reviewBody = "",
    customer = {},
    dates = {},
    reviewUrl = ""
  } = review;
  console.info(review);
  const {
    publishedDate,
    experiencedDate
  } = dates;

  // Format date for display
  const formatDate = dateString => {
    if (!dateString) return "";
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric"
      });
    } catch (error) {
      return "";
    }
  };
  const dynamicRating = (0,_helper_utils__WEBPACK_IMPORTED_MODULE_3__.getRatingText)(rating);

  // Truncate long review text
  const truncateText = (text, maxLength = 500) => {
    if (!text || text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  };
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)("div", {
    className: "review-card",
    "data-review-id": reviewId,
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)("div", {
      className: "review-header",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_CustomerInfo__WEBPACK_IMPORTED_MODULE_2__["default"], {
        customer: customer,
        size: "small"
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)("div", {
        className: "review-meta",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div", {
          className: "dynamic_stars",
          style: {
            width: "150px"
          },
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("img", {
            src: (0,_helper_utils__WEBPACK_IMPORTED_MODULE_3__.getStarImageUrl)(review.rating),
            alt: `${dynamicRating} star rating`,
            style: {
              width: "100%"
            }
          })
        }), publishedDate && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("span", {
          className: "review-date",
          children: formatDate(publishedDate)
        })]
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)("div", {
      className: "review-content",
      children: [reviewTitle && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("h4", {
        className: "review-title",
        children: reviewTitle
      }), reviewBody && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("p", {
        className: "review-body",
        children: truncateText(reviewBody)
      }), experiencedDate && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div", {
        className: "review-experience-date",
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)("small", {
          children: ["Experience date: ", formatDate(experiencedDate)]
        })
      })]
    }), reviewUrl && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div", {
      className: "review-footer",
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("a", {
        href: reviewUrl,
        target: "_blank",
        rel: "noopener noreferrer",
        className: "review-link",
        children: "View on Trustpilot"
      })
    })]
  });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReviewCard);

/***/ }),

/***/ "./react_app/components/containers/reviews/ReviewList.jsx":
/*!****************************************************************!*\
  !*** ./react_app/components/containers/reviews/ReviewList.jsx ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _ReviewCard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ReviewCard */ "./react_app/components/containers/reviews/ReviewCard.jsx");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ "react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__);




/**
 * ReviewList component for displaying a list of reviews
 * @param {Object} reviews - Reviews object from API response (keyed by review ID)
 * @param {number} initialDisplayCount - Initial number of reviews to display (default: 10)
 * @param {boolean} showLoadMore - Whether to show load more button (default: true)
 */

const ReviewList = ({
  reviews = {},
  initialDisplayCount = 10,
  showLoadMore = true
}) => {
  const [displayCount, setDisplayCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialDisplayCount);

  // Convert reviews object to array and sort by published date (newest first)
  const reviewsArray = Object.values(reviews).sort((a, b) => {
    const dateA = new Date(a.dates?.publishedDate || 0);
    const dateB = new Date(b.dates?.publishedDate || 0);
    return dateB - dateA;
  });
  const totalReviews = reviewsArray.length;
  const displayedReviews = reviewsArray.slice(0, displayCount);
  const hasMoreReviews = displayCount < totalReviews;

  // Load more reviews
  const handleLoadMore = () => {
    setDisplayCount(prev => Math.min(prev + initialDisplayCount, totalReviews));
  };

  // Reset to initial count
  const handleShowLess = () => {
    setDisplayCount(initialDisplayCount);
  };

  // Filter reviews by rating
  const [ratingFilter, setRatingFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);
  const filteredReviews = ratingFilter ? displayedReviews.filter(review => review.rating === ratingFilter) : displayedReviews;

  // Get rating distribution
  const getRatingDistribution = () => {
    const distribution = {
      1: 0,
      2: 0,
      3: 0,
      4: 0,
      5: 0
    };
    reviewsArray.forEach(review => {
      if (review.rating >= 1 && review.rating <= 5) {
        distribution[Math.floor(review.rating)]++;
      }
    });
    return distribution;
  };
  const ratingDistribution = getRatingDistribution();
  if (totalReviews === 0) {
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("div", {
      className: "review-list",
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("div", {
        className: "review-list-empty",
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("p", {
          children: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__.__)('>No reviews available', 'reviewkit')
        })
      })
    });
  }
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("div", {
    className: "review-list",
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("div", {
      className: "review-list-header",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("h3", {
        className: "review-list-title",
        children: ["Customer Reviews (", totalReviews, ")"]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("div", {
        className: "review-filters",
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("div", {
          className: "rating-filter",
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("button", {
            className: `filter-btn ${ratingFilter === null ? 'active' : ''}`,
            onClick: () => setRatingFilter(null),
            children: "All"
          }), [5, 4, 3, 2, 1].map(rating => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("button", {
            className: `filter-btn ${ratingFilter === rating ? 'active' : ''}`,
            onClick: () => setRatingFilter(rating),
            children: [rating, "\u2605 (", ratingDistribution[rating], ")"]
          }, rating))]
        })
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("div", {
      className: "review-list-content",
      children: filteredReviews.length === 0 ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("div", {
        className: "review-list-empty",
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("p", {
          children: "No reviews found for the selected rating"
        })
      }) : /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("div", {
        className: "review-cards",
        children: filteredReviews.map(review => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_ReviewCard__WEBPACK_IMPORTED_MODULE_1__["default"], {
          review: review
        }, review.reviewId))
      })
    }), showLoadMore && ratingFilter === null && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("div", {
      className: "review-list-controls",
      children: [hasMoreReviews && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("button", {
        className: "load-more-btn",
        onClick: handleLoadMore,
        children: ["Load More Reviews (", totalReviews - displayCount, " remaining)"]
      }), displayCount > initialDisplayCount && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("button", {
        className: "show-less-btn",
        onClick: handleShowLess,
        children: "Show Less"
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("div", {
      className: "review-summary",
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("p", {
        className: "review-count-summary",
        children: ["Showing ", filteredReviews.length, " of ", totalReviews, " reviews"]
      })
    })]
  });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReviewList);

/***/ }),

/***/ "./react_app/components/containers/reviews/StarRating.jsx":
/*!****************************************************************!*\
  !*** ./react_app/components/containers/reviews/StarRating.jsx ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ "react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__);


/**
 * StarRating component for displaying star ratings with authentic Trustpilot design
 * @param {number} rating - The rating value (0-5)
 * @param {number} maxStars - Maximum number of stars (default: 5)
 * @param {string} size - Size of stars: 'small', 'medium', 'large' (default: 'medium')
 * @param {boolean} showRating - Whether to show the numeric rating (default: false)
 * @param {boolean} useSvg - Whether to use SVG stars instead of Unicode (default: false)
 */

const StarRating = ({
  rating = 0,
  maxStars = 5,
  size = "medium",
  showRating = false,
  useSvg = false
}) => {
  const stars = [];
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 !== 0;

  // Get Trustpilot color based on rating
  const getTrustpilotColor = rating => {
    if (rating <= 1) return "#ff3722"; // Red
    if (rating <= 2) return "#ff8622"; // Orange
    if (rating <= 3) return "#ffce00"; // Yellow
    if (rating <= 4) return "#73cf11"; // Light Green
    return "#00b67a"; // Trustpilot Green
  };
  const starColor = getTrustpilotColor(rating);

  // SVG Star component for authentic Trustpilot look
  const TrustpilotSvgStar = ({
    filled,
    color
  }) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("svg", {
    className: `star-svg ${filled ? 'star-filled' : 'star-empty'}`,
    viewBox: "0 0 24 24",
    style: {
      fill: filled ? color : '#ddd'
    },
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("path", {
      d: "M12 2l2.938 7.968h8.382l-6.76 4.898 2.553 7.834L12 17.334l-7.113 5.366 2.553-7.834-6.76-4.898h8.382z"
    })
  });

  // Generate full stars
  for (let i = 0; i < fullStars; i++) {
    stars.push(useSvg ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TrustpilotSvgStar, {
      filled: true,
      color: starColor
    }, `full-${i}`) : /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("span", {
      className: "star star-full",
      style: {
        color: starColor
      },
      children: "\u2605"
    }, `full-${i}`));
  }

  // Add half star if needed
  if (hasHalfStar && fullStars < maxStars) {
    stars.push(useSvg ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
      className: "star-svg-half",
      style: {
        position: 'relative'
      },
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TrustpilotSvgStar, {
        filled: false,
        color: "#ddd"
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
        style: {
          position: 'absolute',
          top: 0,
          left: 0,
          width: '50%',
          overflow: 'hidden'
        },
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TrustpilotSvgStar, {
          filled: true,
          color: starColor
        })
      })]
    }, "half") : /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("span", {
      className: "star star-half",
      style: {
        color: starColor
      },
      children: "\u2605"
    }, "half"));
  }

  // Generate empty stars
  const emptyStars = maxStars - fullStars - (hasHalfStar ? 1 : 0);
  for (let i = 0; i < emptyStars; i++) {
    stars.push(useSvg ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TrustpilotSvgStar, {
      filled: false,
      color: "#ddd"
    }, `empty-${i}`) : /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("span", {
      className: "star star-empty",
      children: "\u2605"
    }, `empty-${i}`));
  }
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
    className: `trustpilot-star-rating trustpilot-star-rating--${size}`,
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
      className: "stars",
      children: stars
    }), showRating && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("span", {
      className: "rating-value",
      children: ["(", rating, ")"]
    })]
  });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StarRating);

/***/ }),

/***/ "./react_app/components/containers/reviews/TrustpilotBusinessPage.jsx":
/*!****************************************************************************!*\
  !*** ./react_app/components/containers/reviews/TrustpilotBusinessPage.jsx ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _helper_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../helper/utils */ "./react_app/components/helper/utils.js");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ "react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);



/**
 * TrustpilotBusinessPage component for displaying business information
 * @param {Object} businessDetails - Business details object from API response
 */

const TrustpilotBusinessPage = ({
  businessDetails = {}
}) => {
  const {
    displayName = "",
    identifyingName = "",
    numberOfReviews = 0,
    trustScore = 0,
    websiteUrl = "",
    profileImageUrl = "",
    stars = 0,
    categories = [],
    activity = {}
  } = businessDetails;

  // Format large numbers
  const formatNumber = num => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + "M";
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + "K";
    }
    return num.toString();
  };
  const primaryCategory = categories.find(cat => cat.isPrimary)?.name || "";
  const isVerified = activity?.verification?.verifiedUserIdentity || false;
  const isClaimed = activity?.isClaimed || false;
  const dynamicRating = (0,_helper_utils__WEBPACK_IMPORTED_MODULE_0__.getRatingText)(trustScore);

  // Format profile image URL
  const getImageUrl = url => {
    if (!url) return "";
    if (url.startsWith("//")) {
      return `https:${url}`;
    }
    return url;
  };
  if (!displayName) {
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("div", {
      className: "trustpilot-business-page",
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("div", {
        className: "business-placeholder",
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("p", {
          children: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__.__)("No business details found", "reviewkit")
        })
      })
    });
  }
  console.info(activity.replyBehavior);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)("div", {
    className: "trustpilot-business-page",
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)("div", {
      className: "business-header",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)("div", {
        className: "business-logo",
        children: [profileImageUrl ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("img", {
          src: getImageUrl(profileImageUrl),
          alt: `${displayName} logo`,
          className: "business-image",
          onError: e => {
            e.target.style.display = "none";
            e.target.nextSibling.style.display = "flex";
          }
        }) : null, /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("div", {
          className: "business-initials",
          style: {
            display: profileImageUrl ? "none" : "flex"
          },
          children: displayName.charAt(0).toUpperCase()
        })]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)("div", {
        className: "business-info",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)("div", {
          className: "business-name-section",
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("h2", {
            className: "business-name",
            children: displayName
          }), isClaimed && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)("span", {
            className: "business-claimed",
            children: ["\u2713 ", (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__.__)("Claimed", "reviewkit")]
          }), isVerified && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)("span", {
            className: "business-verified",
            children: ["\u2713 ", (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__.__)("Verified", "reviewkit")]
          })]
        }), identifyingName && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("p", {
          className: "business-website",
          children: identifyingName
        }), primaryCategory && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("p", {
          className: "business-category",
          children: primaryCategory
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)("div", {
          className: "business-rating",
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)("div", {
            className: "rating-percentage",
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("div", {
              className: "dynamic_stars",
              style: {
                width: "200px"
              },
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("img", {
                src: (0,_helper_utils__WEBPACK_IMPORTED_MODULE_0__.getStarImageUrl)(trustScore),
                alt: `${dynamicRating} star rating`
              })
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("span", {
              children: trustScore
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)("span", {
            className: "review-count",
            children: [(0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__.__)("Based on", "reviewkit"), " ", formatNumber(numberOfReviews), " ", (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__.__)("reviews", "reviewkit")]
          })]
        }), websiteUrl && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("div", {
          className: "business-links",
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("a", {
            href: websiteUrl,
            target: "_blank",
            rel: "noopener noreferrer",
            className: "components-button is-secondary business-website-link",
            children: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__.__)("Visit Website", "reviewkit")
          })
        })]
      })]
    }), activity?.replyBehavior && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)("div", {
      className: "business-stats",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)("div", {
        className: "stat-item",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)("span", {
          className: "stat-label",
          children: [(0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__.__)("Reply Rate", "reviewkit"), ":"]
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)("span", {
          className: "stat-value",
          children: [activity?.replyBehavior?.replyPercentage?.toFixed(2), "%"]
        })]
      }), activity.replyBehavior?.averageDaysToReply >= 0 && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)("div", {
        className: "stat-item",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)("span", {
          className: "stat-label",
          children: [(0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__.__)("Avg. Reply Time", "reviewkit"), ":"]
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)("span", {
          className: "stat-value",
          children: [Math.round(activity.replyBehavior.averageDaysToReply), " ", (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__.__)("days", "reviewkit")]
        })]
      })]
    })]
  });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TrustpilotBusinessPage);

/***/ }),

/***/ "./react_app/components/context/data-context.jsx":
/*!*******************************************************!*\
  !*** ./react_app/components/context/data-context.jsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DataContextProvider: () => (/* binding */ DataContextProvider),
/* harmony export */   useDataContext: () => (/* binding */ useDataContext)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _helper_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../helper/utils */ "./react_app/components/helper/utils.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ "react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);

const DataContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)();


function DataContextProvider({
  children
}) {
  const [tab, setTab] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)("reviews");
  const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(_helper_utils__WEBPACK_IMPORTED_MODULE_1__.data || {});
  const value = {
    tab,
    setTab,
    data,
    setData
  };
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(DataContext.Provider, {
    value: value,
    children: children
  });
}
function useDataContext() {
  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(DataContext);
}


/***/ }),

/***/ "./react_app/components/helper/utils.js":
/*!**********************************************!*\
  !*** ./react_app/components/helper/utils.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ajaxurl: () => (/* binding */ ajaxurl),
/* harmony export */   count: () => (/* binding */ count),
/* harmony export */   data: () => (/* binding */ data),
/* harmony export */   domain: () => (/* binding */ domain),
/* harmony export */   formatReviewCount: () => (/* binding */ formatReviewCount),
/* harmony export */   getRatingText: () => (/* binding */ getRatingText),
/* harmony export */   getReviews: () => (/* binding */ getReviews),
/* harmony export */   getReviewsFromAPI: () => (/* binding */ getReviewsFromAPI),
/* harmony export */   getStarImageUrl: () => (/* binding */ getStarImageUrl),
/* harmony export */   last_updated: () => (/* binding */ last_updated),
/* harmony export */   nonce: () => (/* binding */ nonce),
/* harmony export */   original_domain: () => (/* binding */ original_domain),
/* harmony export */   plugin_root_url: () => (/* binding */ plugin_root_url)
/* harmony export */ });
const {
  ajaxurl,
  nonce,
  plugin_root_url,
  data,
  count,
  last_updated,
  domain,
  original_domain
} = window.trustpilot_reviewkit;
/**
 * Utility functions for the Trustpilot ReviewKit
 */

/**
 * Fetches Trustpilot reviews for a specified domain using direct API call
 *
 * @param {string} domain - The domain to fetch reviews for (e.g., "divinext.com")
 * @param {Object} options - Optional configuration options
 * @param {number} options.timeout - Request timeout in milliseconds (default: 10000)
 * @param {AbortSignal} options.signal - AbortController signal for cancelling the request
 * @returns {Promise<Object>} - Promise that resolves to the reviews data
 * @throws {Error} - Throws an error if the request fails
 */
const getReviewsFromAPI = async (domain, options = {}) => {
  if (!domain) {
    throw new Error("Domain is required");
  }

  // Remove any protocol and trailing slashes to ensure consistent format
  const cleanDomain = domain.replace(/^(https?:\/\/)?(www\.)?/, "").replace(/\/$/, "");
  const {
    timeout = 10000,
    signal
  } = options;
  try {
    // Create a controller for timeout if not provided externally
    const controller = signal ? null : new AbortController();
    const timeoutId = signal ? null : setTimeout(() => controller.abort(), timeout);
    const response = await fetch(`https://api.gutensuite.net/data/trustpilot/v1/reviews/${encodeURIComponent(cleanDomain)}`, {
      method: "GET",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json"
      },
      signal: signal || controller.signal
    });

    // Clear timeout if we created one
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    if (!response.ok) {
      throw new Error(`Failed to fetch reviews: ${response.status} ${response.statusText}`);
    }
    const data = await response.json();
    return data;
  } catch (error) {
    if (error.name === "AbortError") {
      throw new Error("Request timed out");
    }
    throw error;
  }
};

/**
 * Fetches Trustpilot reviews for a specified domain using WordPress AJAX
 *
 * @param {string} domain - The domain to fetch reviews for (e.g., "divinext.com")
 * @param {Object} options - Optional configuration options
 * @param {number} options.timeout - Request timeout in milliseconds (default: 30000)
 * @returns {Promise<Object>} - Promise that resolves to the reviews data
 * @throws {Error} - Throws an error if the request fails
 */
const getReviews = (domain, options = {}) => {
  if (!domain) {
    return Promise.reject(new Error("Domain is required"));
  }
  const {
    timeout = 30000
  } = options;

  // Ensure we have access to WordPress ajax functionality
  if (typeof window.ajaxurl === "undefined" || typeof window.trustpilot_reviewkit === "undefined") {
    return Promise.reject(new Error("WordPress AJAX not available. Make sure the script is properly enqueued."));
  }
  return new Promise((resolve, reject) => {
    // Create a FormData object for the request
    const formData = new FormData();
    formData.append("action", "trustpilot_reviewkit_get_reviews");
    formData.append("security", nonce);
    formData.append("domain", domain);
    formData.append("revalidate", options.revalidate);

    // Use jQuery if available, otherwise use fetch
    if (window.jQuery) {
      jQuery.ajax({
        url: window.ajaxurl,
        type: "POST",
        data: formData,
        processData: false,
        contentType: false,
        timeout: timeout,
        success: function (response) {
          if (response.success) {
            resolve(response.data);
          } else {
            reject(new Error(response.data?.message || "Unknown error occurred"));
          }
        },
        error: function (xhr, status, error) {
          if (status === "timeout") {
            reject(new Error("Request timed out"));
          } else {
            reject(new Error(`AJAX request failed: ${error}`));
          }
        }
      });
    } else {
      // Fallback to using fetch API if jQuery is not available
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);
      fetch(window.ajaxurl, {
        method: "POST",
        body: formData,
        signal: controller.signal
      }).then(response => {
        clearTimeout(timeoutId);
        if (!response.ok) {
          throw new Error(`Server returned ${response.status}: ${response.statusText}`);
        }
        return response.json();
      }).then(response => {
        if (response.success) {
          resolve(response.data);
        } else {
          reject(new Error(response.data?.message || "Unknown error occurred"));
        }
      }).catch(error => {
        clearTimeout(timeoutId);
        if (error.name === "AbortError") {
          reject(new Error("Request timed out"));
        } else {
          reject(error);
        }
      });
    }
  });
};

// Format review count
const formatReviewCount = count => {
  if (count >= 1000000) {
    return (count / 1000000).toFixed(1) + "M";
  } else if (count >= 1000) {
    return (count / 1000).toFixed(1) + "K";
  }
  return count.toString();
};
const getStarImageUrl = rating => {
  const roundedRating = Math.round(rating * 2) / 2; // Round to nearest 0.5
  // const filename = `stars-${roundedRating.toFixed(1).replace(".", "_")}.svg`;
  const filename = `stars-${roundedRating.toFixed(1)}.svg`;
  return plugin_root_url + "/assets/images/" + filename;
};

// Get rating text based on score
const getRatingText = score => {
  if (score >= 4.5) return "Excellent";
  if (score >= 4.0) return "Great";
  if (score >= 3.0) return "Good";
  if (score >= 2.0) return "Average";
  return "Poor";
};

/***/ }),

/***/ "./react_app/components/pages/Layout.jsx":
/*!***********************************************!*\
  !*** ./react_app/components/pages/Layout.jsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _context_data_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../context/data-context */ "./react_app/components/context/data-context.jsx");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ "react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);



const Layout = () => {
  const {
    tab,
    setTab
  } = (0,_context_data_context__WEBPACK_IMPORTED_MODULE_1__.useDataContext)();
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("nav", {
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("ul", {
      children: menuItems.map(item => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("li", {
        className: `${tab === item.tab ? "active" : ""}`,
        onClick: () => setTab(item.tab),
        children: item.label
      }, item.label))
    })
  });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);
const menuItems = [{
  label: "Reviews",
  tab: "reviews"
}, {
  label: "Shortcodes",
  tab: "shortcodes"
}];

/***/ }),

/***/ "./react_app/components/pages/reviews/index.jsx":
/*!******************************************************!*\
  !*** ./react_app/components/pages/reviews/index.jsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _wordpress_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/components */ "@wordpress/components");
/* harmony import */ var _wordpress_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_components__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _helper_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../helper/utils */ "./react_app/components/helper/utils.js");
/* harmony import */ var _context_data_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../context/data-context */ "./react_app/components/context/data-context.jsx");
/* harmony import */ var _containers_reviews_TrustpilotBusinessPage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../containers/reviews/TrustpilotBusinessPage */ "./react_app/components/containers/reviews/TrustpilotBusinessPage.jsx");
/* harmony import */ var _containers_reviews_ReviewList__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../containers/reviews/ReviewList */ "./react_app/components/containers/reviews/ReviewList.jsx");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react/jsx-runtime */ "react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__);








const Reviews = () => {
  const {
    data = {},
    setData
  } = (0,_context_data_context__WEBPACK_IMPORTED_MODULE_4__.useDataContext)();
  const {
    business_details = {},
    reviews = []
  } = data;
  const [trustpilotUrl, setTrustpilotUrl] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(_helper_utils__WEBPACK_IMPORTED_MODULE_3__.original_domain);
  const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);
  const __fetchReviews = async (revalidate = false) => {
    setLoading(true);
    const response = await (0,_helper_utils__WEBPACK_IMPORTED_MODULE_3__.getReviews)(trustpilotUrl, {
      revalidate
    });
    if (response?.data) {
      setData(response.data);
    }
    setLoading(false);
  };
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxs)("div", {
    className: "page review-page",
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxs)("div", {
      className: "review-fetch",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_wordpress_components__WEBPACK_IMPORTED_MODULE_1__.TextControl, {
        __nextHasNoMarginBottom: true,
        __next40pxDefaultSize: true,
        label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__.__)("Trustpilot URL", "trustpilot-reviewkit"),
        type: "url",
        help: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__.__)("Enter the URL of your Trustpilot page", "trustpilot-reviewkit"),
        value: trustpilotUrl,
        onChange: value => setTrustpilotUrl(value)
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxs)("div", {
        className: "button-container",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_wordpress_components__WEBPACK_IMPORTED_MODULE_1__.Button, {
          variant: "primary",
          style: {
            backgroundColor: "#28a745",
            fontWeight: "600"
          },
          onClick: __fetchReviews,
          disabled: trustpilotUrl === "",
          children: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__.__)("Fetch Reviews", "trustpilot-reviewkit")
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_wordpress_components__WEBPACK_IMPORTED_MODULE_1__.Button, {
          variant: "secondary",
          onClick: () => __fetchReviews(true),
          disabled: !data,
          children: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__.__)("Revalidate Reviews", "trustpilot-reviewkit")
        })]
      })]
    }), loading ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)("div", {
      style: {
        marginTop: "20px",
        textAlign: "center"
      },
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_wordpress_components__WEBPACK_IMPORTED_MODULE_1__.Spinner, {
        style: {
          height: "calc(4px * 10)",
          width: "calc(4px * 10)"
        }
      })
    }) : /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.Fragment, {
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_containers_reviews_TrustpilotBusinessPage__WEBPACK_IMPORTED_MODULE_5__["default"], {
        businessDetails: business_details
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_containers_reviews_ReviewList__WEBPACK_IMPORTED_MODULE_6__["default"], {
        reviews: reviews
      })]
    })]
  });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Reviews);

/***/ }),

/***/ "./react_app/components/pages/shortcodes/index.jsx":
/*!*********************************************************!*\
  !*** ./react_app/components/pages/shortcodes/index.jsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _wordpress_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/components */ "@wordpress/components");
/* harmony import */ var _wordpress_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_components__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _shortcode_previews_MiniTrustboxPreview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shortcode-previews/MiniTrustboxPreview */ "./react_app/components/shortcode-previews/MiniTrustboxPreview.jsx");
/* harmony import */ var _shortcode_previews_StarterTrustboxPreview__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../shortcode-previews/StarterTrustboxPreview */ "./react_app/components/shortcode-previews/StarterTrustboxPreview.jsx");
/* harmony import */ var _shortcode_previews_MicroTrustScorePreview__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../shortcode-previews/MicroTrustScorePreview */ "./react_app/components/shortcode-previews/MicroTrustScorePreview.jsx");
/* harmony import */ var _shortcode_previews_MicroStarPreview__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../shortcode-previews/MicroStarPreview */ "./react_app/components/shortcode-previews/MicroStarPreview.jsx");
/* harmony import */ var _shortcode_previews_MicroReviewCountPreview__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../shortcode-previews/MicroReviewCountPreview */ "./react_app/components/shortcode-previews/MicroReviewCountPreview.jsx");
/* harmony import */ var _shortcode_previews_MicroButtonPreview__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../shortcode-previews/MicroButtonPreview */ "./react_app/components/shortcode-previews/MicroButtonPreview.jsx");
/* harmony import */ var _shortcode_previews_MicroComboPreview__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../shortcode-previews/MicroComboPreview */ "./react_app/components/shortcode-previews/MicroComboPreview.jsx");
/* harmony import */ var _helper_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../helper/utils */ "./react_app/components/helper/utils.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react/jsx-runtime */ "react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__);




// Import preview components









const ShortCodes = () => {
  const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)("");
  const [copiedShortcode, setCopiedShortcode] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)("");

  // Shortcode data
  const shortcodes = [{
    id: "mini",
    name: "Mini Trustbox",
    shortcode: "[reviewkit_trustpilot_mini]",
    description: "Compact widget showing TrustScore, star rating, and review count",
    category: "free",
    preview: _shortcode_previews_MiniTrustboxPreview__WEBPACK_IMPORTED_MODULE_3__["default"],
    attributes: [{
      name: "trustscore",
      default: "4.8",
      description: "Trust score rating (0-5)"
    }, {
      name: "reviews",
      default: "347",
      description: "Number of reviews"
    }, {
      name: "url",
      default: "#",
      description: "Link URL"
    }]
  }, {
    id: "starter",
    name: "Starter Trustbox",
    shortcode: "[reviewkit_trustpilot_starter]",
    description: "Interactive widget with star rating and tooltip",
    category: "free",
    preview: _shortcode_previews_StarterTrustboxPreview__WEBPACK_IMPORTED_MODULE_4__["default"],
    attributes: [{
      name: "reviews",
      default: "1,376",
      description: "Number of reviews"
    }, {
      name: "url",
      default: "#",
      description: "Link URL"
    }]
  }, {
    id: "micro_trustscore",
    name: "Micro TrustScore",
    shortcode: "[reviewkit_trustpilot_micro_trustscore]",
    description: "Simple display of TrustScore rating",
    category: "free",
    preview: _shortcode_previews_MicroTrustScorePreview__WEBPACK_IMPORTED_MODULE_5__["default"],
    attributes: [{
      name: "rating",
      default: "Excellent",
      description: "Rating text"
    }, {
      name: "score",
      default: "4.8",
      description: "Numeric score"
    }, {
      name: "url",
      default: "#",
      description: "Link URL"
    }]
  }, {
    id: "micro_star",
    name: "Micro Star",
    shortcode: "[reviewkit_trustpilot_micro_star]",
    description: "Interactive star rating display",
    category: "free",
    preview: _shortcode_previews_MicroStarPreview__WEBPACK_IMPORTED_MODULE_6__["default"],
    attributes: [{
      name: "rating",
      default: "Excellent",
      description: "Rating text"
    }, {
      name: "stars",
      default: "5",
      description: "Number of stars (1-5)"
    }, {
      name: "url",
      default: "#",
      description: "Link URL"
    }]
  }, {
    id: "micro_reviewcount",
    name: "Micro Review Count",
    shortcode: "[reviewkit_trustpilot_micro_reviewcount]",
    description: "Link showing review count",
    category: "free",
    preview: _shortcode_previews_MicroReviewCountPreview__WEBPACK_IMPORTED_MODULE_7__["default"],
    attributes: [{
      name: "count",
      default: "437",
      description: "Number of reviews"
    }, {
      name: "url",
      default: "#",
      description: "Link URL"
    }]
  }, {
    id: "micro_button",
    name: "Micro Button",
    shortcode: "[reviewkit_trustpilot_micro_button]",
    description: "Button-style widget with review count",
    category: "free",
    preview: _shortcode_previews_MicroButtonPreview__WEBPACK_IMPORTED_MODULE_8__["default"],
    attributes: [{
      name: "reviews",
      default: "1.3K+",
      description: "Review count display"
    }, {
      name: "url",
      default: "#",
      description: "Link URL"
    }]
  }, {
    id: "micro_combo",
    name: "Micro Combo",
    shortcode: "[reviewkit_trustpilot_micro_combo]",
    description: "Combined star rating and review count widget",
    category: "free",
    preview: _shortcode_previews_MicroComboPreview__WEBPACK_IMPORTED_MODULE_9__["default"],
    attributes: [{
      name: "rating",
      default: "Excellent",
      description: "Rating text"
    }, {
      name: "stars",
      default: "5",
      description: "Number of stars (1-5)"
    }, {
      name: "reviews",
      default: "437",
      description: "Number of reviews"
    }, {
      name: "url",
      default: "#",
      description: "Link URL"
    }]
  }];

  // Filter shortcodes based on search term
  const filteredShortcodes = shortcodes.filter(shortcode => shortcode.name.toLowerCase().includes(searchTerm.toLowerCase()) || shortcode.description.toLowerCase().includes(searchTerm.toLowerCase()) || shortcode.shortcode.toLowerCase().includes(searchTerm.toLowerCase()));

  // Copy shortcode to clipboard with fallback methods
  const copyToClipboard = async shortcode => {
    try {
      // Method 1: Modern Clipboard API
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(shortcode);
        setCopiedShortcode(shortcode);
        setTimeout(() => setCopiedShortcode(""), 2000);
        return;
      }

      // Method 2: Fallback using document.execCommand (deprecated but widely supported)
      const textArea = document.createElement("textarea");
      textArea.value = shortcode;
      textArea.style.position = "fixed";
      textArea.style.left = "-999999px";
      textArea.style.top = "-999999px";
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      const successful = document.execCommand("copy");
      document.body.removeChild(textArea);
      if (successful) {
        setCopiedShortcode(shortcode);
        setTimeout(() => setCopiedShortcode(""), 2000);
      } else {
        throw new Error("execCommand failed");
      }
    } catch (err) {
      console.error("Failed to copy shortcode: ", err);
      // Method 3: Final fallback - show alert with shortcode
      alert(`Copy this shortcode manually: ${shortcode}`);
    }
  };
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsxs)("div", {
    className: "page shortcodes-page",
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsxs)("div", {
      className: "shortcodes-header",
      style: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      },
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsxs)("div", {
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)("h2", {
          children: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__.__)("Trustpilot Shortcodes", "trustpilot-reviewkit")
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)("p", {
          children: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__.__)("Use these shortcodes to display Trustpilot widgets on your website.", "trustpilot-reviewkit")
        })]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_wordpress_components__WEBPACK_IMPORTED_MODULE_1__.TextControl
      // label={__("Search Shortcodes", "trustpilot-reviewkit")}
      , {
        value: searchTerm,
        onChange: setSearchTerm,
        placeholder: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__.__)("Search by name, description, or shortcode...", "trustpilot-reviewkit"),
        className: "shortcodes-search"
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)("div", {
      className: "shortcodes-grid",
      children: filteredShortcodes.map(shortcode => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsxs)(_wordpress_components__WEBPACK_IMPORTED_MODULE_1__.Card, {
        className: `shortcode-card ${shortcode.id}`,
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsxs)(_wordpress_components__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)("h3", {
            children: shortcode.name
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)("span", {
            className: "shortcode-category",
            children: shortcode.category
          })]
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsxs)(_wordpress_components__WEBPACK_IMPORTED_MODULE_1__.CardBody, {
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)("div", {
            className: "shortcode-preview",
            children: shortcode.preview ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)("div", {
              className: "preview-component",
              style: {
                overflow: "visible"
              },
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(shortcode.preview, {})
            }) : /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsxs)("div", {
              className: "preview-placeholder",
              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)("span", {
                children: "\uD83D\uDCCA"
              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)("small", {
                children: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__.__)("Preview", "trustpilot-reviewkit")
              })]
            })
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)("p", {
            className: "shortcode-description",
            children: shortcode.description
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsxs)("div", {
            className: "shortcode-code",
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)("code", {
              children: shortcode.shortcode
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_wordpress_components__WEBPACK_IMPORTED_MODULE_1__.Button, {
              variant: "secondary",
              size: "small",
              onClick: () => copyToClipboard(shortcode.shortcode),
              className: copiedShortcode === shortcode.shortcode ? "copied" : "",
              children: copiedShortcode === shortcode.shortcode ? (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__.__)("Copied!", "trustpilot-reviewkit") : (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__.__)("Copy", "trustpilot-reviewkit")
            })]
          })]
        })]
      }, shortcode.id))
    }), filteredShortcodes.length === 0 && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)("div", {
      className: "no-results",
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)("p", {
        children: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__.__)("No shortcodes found matching your search.", "trustpilot-reviewkit")
      })
    })]
  });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ShortCodes);

/***/ }),

/***/ "./react_app/components/shortcode-previews/MicroButtonPreview.jsx":
/*!************************************************************************!*\
  !*** ./react_app/components/shortcode-previews/MicroButtonPreview.jsx ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _containers_preview_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../containers/preview/image */ "./react_app/components/containers/preview/image.jsx");
/* harmony import */ var _helper_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../helper/utils */ "./react_app/components/helper/utils.js");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ "react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__);




/**
 * Micro Button Preview Component
 * Mirrors the exact HTML structure from Reviews-kit/microbutton.html
 */

const MicroButtonPreview = ({
  reviews
}) => {
  const businessData = _helper_utils__WEBPACK_IMPORTED_MODULE_2__.data?.business_details || {};
  const dynamicReviews = reviews || businessData.numberOfReviews || 1300;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("div", {
    className: "reviewkit_fpln_mcb_wrap",
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("a", {
      className: "reviewkit_fpln_mcb_left",
      href: "#",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_containers_preview_image__WEBPACK_IMPORTED_MODULE_1__["default"], {
        type: "light"
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("span", {
        className: "place_name",
        children: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_0__.__)("Trustpilot", "reviewkit")
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("div", {
      className: "reviewkit_fpln_mcb_right",
      children: [(0,_helper_utils__WEBPACK_IMPORTED_MODULE_2__.formatReviewCount)(dynamicReviews), " ", (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_0__.__)("reviews", "reviewkit")]
    })]
  });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MicroButtonPreview);

/***/ }),

/***/ "./react_app/components/shortcode-previews/MicroComboPreview.jsx":
/*!***********************************************************************!*\
  !*** ./react_app/components/shortcode-previews/MicroComboPreview.jsx ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _helper_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../helper/utils */ "./react_app/components/helper/utils.js");
/* harmony import */ var _containers_preview_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../containers/preview/image */ "./react_app/components/containers/preview/image.jsx");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ "react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__);




/**
 * Micro Combo Preview Component
 * Mirrors the exact HTML structure from Reviews-kit/microcombo.html
 */

const MicroComboPreview = ({
  rating,
  stars,
  reviews
}) => {
  const businessData = _helper_utils__WEBPACK_IMPORTED_MODULE_0__.data?.business_details || {};
  const dynamicScore = businessData.trustScore || 4.8;
  const dynamicReviews = reviews || businessData.numberOfReviews || 437;
  const dynamicRating = rating || (0,_helper_utils__WEBPACK_IMPORTED_MODULE_0__.getRatingText)(dynamicScore);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("div", {
    className: "reviewkit_fpln_mc reviewkit_fpln_common preview",
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("div", {
      className: "reviewkit_fpln_mc_inner_left",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("span", {
        className: "review_us_one",
        children: dynamicRating
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("div", {
        className: "reviewkit_star_rating",
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("img", {
          src: (0,_helper_utils__WEBPACK_IMPORTED_MODULE_0__.getStarImageUrl)(dynamicScore),
          alt: `${dynamicRating} star rating`,
          style: {
            width: "100%"
          }
        })
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("div", {
      className: "reviewkit_fpln_mc_inner_right",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("span", {
        className: "mirc_r_count",
        children: (0,_helper_utils__WEBPACK_IMPORTED_MODULE_0__.formatReviewCount)(dynamicReviews)
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("span", {
        className: "review_us_one",
        children: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__.__)("reviews on", "reviewkit")
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_containers_preview_image__WEBPACK_IMPORTED_MODULE_1__["default"], {
        style: {
          width: "18px"
        }
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("span", {
        className: "place_name",
        children: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__.__)("Trustpilot", "reviewkit")
      })]
    })]
  });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MicroComboPreview);

/***/ }),

/***/ "./react_app/components/shortcode-previews/MicroReviewCountPreview.jsx":
/*!*****************************************************************************!*\
  !*** ./react_app/components/shortcode-previews/MicroReviewCountPreview.jsx ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _helper_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../helper/utils */ "./react_app/components/helper/utils.js");
/* harmony import */ var _containers_preview_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../containers/preview/image */ "./react_app/components/containers/preview/image.jsx");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ "react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__);



/**
 * Micro Review Count Preview Component
 * Mirrors the exact HTML structure from Reviews-kit/microreviewcount.html
 */

const MicroReviewCountPreview = ({
  count
}) => {
  // Get dynamic business data
  const businessData = _helper_utils__WEBPACK_IMPORTED_MODULE_0__.data?.business_details || {};
  const dynamicCount = count || businessData.numberOfReviews || 437;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("div", {
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("a", {
      className: "reviewkit_fpln_mirc reviewkit_fpln_common preview",
      href: "#",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("span", {
        className: "mirc_see",
        children: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__.__)("See our", "reviewkit")
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("span", {
        className: "mirc_r_count",
        children: (0,_helper_utils__WEBPACK_IMPORTED_MODULE_0__.formatReviewCount)(dynamicCount)
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("span", {
        className: "review_us_one",
        children: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__.__)("reviews on", "reviewkit")
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_containers_preview_image__WEBPACK_IMPORTED_MODULE_1__["default"], {
        style: {
          width: "20px"
        }
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("span", {
        className: "place_name",
        children: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__.__)("Trustpilot", "reviewkit")
      })]
    })
  });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MicroReviewCountPreview);

/***/ }),

/***/ "./react_app/components/shortcode-previews/MicroStarPreview.jsx":
/*!**********************************************************************!*\
  !*** ./react_app/components/shortcode-previews/MicroStarPreview.jsx ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _helper_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../helper/utils */ "./react_app/components/helper/utils.js");
/* harmony import */ var _containers_preview_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../containers/preview/image */ "./react_app/components/containers/preview/image.jsx");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ "react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__);




/**
 * Micro Star Preview Component
 * Mirrors the exact HTML structure from Reviews-kit/microstar.html
 */

const MicroStarPreview = ({
  rating,
  stars
}) => {
  const businessData = _helper_utils__WEBPACK_IMPORTED_MODULE_1__.data?.business_details || {};
  const dynamicScore = businessData.trustScore || 4.8;
  const dynamicRating = rating || (0,_helper_utils__WEBPACK_IMPORTED_MODULE_1__.getRatingText)(dynamicScore);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("div", {
    className: "reviewkit_fpln_mcs reviewkit_fpln_common preview",
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("div", {
      className: "dynamic_rating",
      children: dynamicRating
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("div", {
      className: "dynamic_stars",
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("img", {
        src: (0,_helper_utils__WEBPACK_IMPORTED_MODULE_1__.getStarImageUrl)(dynamicScore),
        alt: `${dynamicRating} star rating`,
        style: {
          width: "100%"
        }
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("div", {
      className: "tp-wrapper",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_containers_preview_image__WEBPACK_IMPORTED_MODULE_2__["default"], {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("div", {
        className: "place_name",
        children: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_0__.__)("Trustpilot", "reviewkit")
      })]
    })]
  });

  // return (
  // 	<div className="reviewkit_fpln_mcs reviewkit_fpln_common preview">
  // 		{/* Left section with rating and stars */}
  // 		<div className="reviewkit_fpln_mc_inner_left">
  // 			<span className="review_us_one">{dynamicRating}</span>
  // 			<div className="reviewkit_star_rating">
  // 				<img
  // 					src={getStarImageUrl(dynamicScore)}
  // 					alt={`${dynamicRating} star rating`}
  // 					style={{ width: "100%" }}
  // 				/>
  // 			</div>
  // 		</div>

  // 		{/* Right section with Trustpilot logo */}
  // 		<div className="reviewkit_fpln_mc_inner_right">
  // 			<span className="review_us_one"></span>
  // 			<svg className="reviewkit_fpln_star_icon" viewBox="0 0 24 24">
  // 				<path d="M12 2l2.938 7.968h8.382l-6.76 4.898 2.553 7.834L12 17.334l-7.113 5.366 2.553-7.834-6.76-4.898h8.382z" />
  // 			</svg>
  // 			<span className="place_name">Trustpilot</span>
  // 		</div>
  // 	</div>
  // );
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MicroStarPreview);

/***/ }),

/***/ "./react_app/components/shortcode-previews/MicroTrustScorePreview.jsx":
/*!****************************************************************************!*\
  !*** ./react_app/components/shortcode-previews/MicroTrustScorePreview.jsx ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _helper_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../helper/utils */ "./react_app/components/helper/utils.js");
/* harmony import */ var _containers_preview_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../containers/preview/image */ "./react_app/components/containers/preview/image.jsx");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ "react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__);




/**
 * Micro TrustScore Preview Component
 * Mirrors the exact HTML structure from Reviews-kit/microtrustscore.html
 */

const MicroTrustScorePreview = ({
  rating,
  score
}) => {
  // Get dynamic business data
  const businessData = _helper_utils__WEBPACK_IMPORTED_MODULE_1__.data?.business_details || {};
  const dynamicScore = score || businessData.trustScore || 4.8;

  // Get rating text based on score
  const getRatingText = score => {
    if (score >= 4.5) return "Excellent";
    if (score >= 4.0) return "Great";
    if (score >= 3.0) return "Good";
    if (score >= 2.0) return "Average";
    return "Poor";
  };
  const dynamicRating = rating || getRatingText(dynamicScore);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("div", {
    className: "reviewkit_fpln_mcts reviewkit_fpln_common preview",
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("div", {
      className: "reviewkit_fpln_inner_left",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("span", {
        className: "review_us_one",
        children: dynamicRating
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("div", {
        className: "reviewkit_score",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("span", {
          className: "reviewkit_orignal_rcount",
          children: dynamicScore
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("span", {
          className: "reviewkit_out_of",
          children: " out of 5"
        })]
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("div", {
      className: "reviewkit_fpln_inner_right",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_containers_preview_image__WEBPACK_IMPORTED_MODULE_2__["default"], {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("span", {
        className: "place_name",
        children: "Trustpilot"
      })]
    })]
  });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MicroTrustScorePreview);

/***/ }),

/***/ "./react_app/components/shortcode-previews/MiniTrustboxPreview.jsx":
/*!*************************************************************************!*\
  !*** ./react_app/components/shortcode-previews/MiniTrustboxPreview.jsx ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _containers_preview_image__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../containers/preview/image */ "./react_app/components/containers/preview/image.jsx");
/* harmony import */ var _helper_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../helper/utils */ "./react_app/components/helper/utils.js");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ "react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__);




/**
 * Mini Trustbox Preview Component
 * Mirrors the exact HTML structure from Reviews-kit/mini.html
 */

const MiniTrustboxPreview = ({
  trustscore,
  reviews
}) => {
  const businessData = _helper_utils__WEBPACK_IMPORTED_MODULE_1__.data?.business_details || {};
  const dynamicTrustScore = trustscore || businessData.trustScore || 4.8;
  const dynamicReviews = reviews || businessData.numberOfReviews || 347;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("div", {
    className: "reviewkit_fpln_mini preview",
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("div", {
      className: "reviewkit_fpln_inner_top",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_containers_preview_image__WEBPACK_IMPORTED_MODULE_0__["default"], {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("span", {
        className: "place_name",
        children: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__.__)("Trustpilot", "reviewkit")
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("div", {
      className: "reviewkit_bg",
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("img", {
        src: (0,_helper_utils__WEBPACK_IMPORTED_MODULE_1__.getStarImageUrl)(dynamicTrustScore),
        alt: `${dynamicTrustScore} star rating`
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("div", {
      className: "reviewkit_fpln_inner_bottom",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("div", {
        className: "reviewkit_left_reviews",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("span", {
          className: "review_us_one",
          children: [(0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__.__)("TrustScore", "reviewkit"), " "]
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("span", {
          className: "reviewkit_orignal_rcount",
          children: dynamicTrustScore
        })]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("div", {
        className: "reviewkit_review_area",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)("span", {
          className: "reviewkit_out_of",
          children: (0,_helper_utils__WEBPACK_IMPORTED_MODULE_1__.formatReviewCount)(dynamicReviews)
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)("span", {
          className: "reviewkit_reviews",
          children: [" ", (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_2__.__)("reviews", "reviewkit")]
        })]
      })]
    })]
  });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MiniTrustboxPreview);

/***/ }),

/***/ "./react_app/components/shortcode-previews/StarterTrustboxPreview.jsx":
/*!****************************************************************************!*\
  !*** ./react_app/components/shortcode-previews/StarterTrustboxPreview.jsx ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _helper_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../helper/utils */ "./react_app/components/helper/utils.js");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ "react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);


/**
 * Starter Trustbox Preview Component
 * Mirrors the exact HTML structure from Reviews-kit/starter.html
 */

const StarterTrustboxPreview = ({
  reviews
}) => {
  const businessData = _helper_utils__WEBPACK_IMPORTED_MODULE_0__.data?.business_details || {};
  const dynamicReviews = reviews || businessData.numberOfReviews || 1376;
  const dynamicTrustScore = businessData.trustScore || 4.8;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)("div", {
    className: "reviewkit_fpln_starter preview",
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)("div", {
      className: "reviewkit_fpln_inner_top",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)("span", {
        className: "mirc_see",
        children: [(0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__.__)("Check out our", "reviewkit"), " "]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("span", {
        className: "mirc_r_count",
        children: (0,_helper_utils__WEBPACK_IMPORTED_MODULE_0__.formatReviewCount)(dynamicReviews)
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)("span", {
        className: "review_us_one",
        children: [" ", (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__.__)("reviews", "reviewkit")]
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("div", {
      className: "reviewkit_bg",
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("img", {
        src: (0,_helper_utils__WEBPACK_IMPORTED_MODULE_0__.getStarImageUrl)(dynamicTrustScore),
        alt: `${dynamicTrustScore} star rating`
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("div", {
      className: "reviewkit_toltip_wrap",
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)("div", {
        className: "reviewkit_logo_container",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Tooltip, {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("img", {
          src: _helper_utils__WEBPACK_IMPORTED_MODULE_0__.plugin_root_url + "/assets/images/single-star.svg",
          alt: "Trustpilot Logo"
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("span", {
          className: "reviewkit_text",
          children: "Trustpilot"
        })]
      })
    })]
  });
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StarterTrustboxPreview);
const Tooltip = () => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)("div", {
  className: "reviewkit_tooltip reviewkit_tooltip_large",
  children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("div", {
    className: "reviewkit_tooltip_content",
    children: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__.__)("Helping each other make better choices", "reviewkit")
  }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("a", {
    href: "#",
    className: "reviewkit_tooltip_link",
    children: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_1__.__)("Read and write reviews", "reviewkit")
  })]
});

/***/ }),

/***/ "@wordpress/components":
/*!************************************!*\
  !*** external ["wp","components"] ***!
  \************************************/
/***/ ((module) => {

module.exports = window["wp"]["components"];

/***/ }),

/***/ "@wordpress/i18n":
/*!******************************!*\
  !*** external ["wp","i18n"] ***!
  \******************************/
/***/ ((module) => {

module.exports = window["wp"]["i18n"];

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "React" ***!
  \************************/
/***/ ((module) => {

module.exports = window["React"];

/***/ }),

/***/ "react-dom":
/*!***************************!*\
  !*** external "ReactDOM" ***!
  \***************************/
/***/ ((module) => {

module.exports = window["ReactDOM"];

/***/ }),

/***/ "react/jsx-runtime":
/*!**********************************!*\
  !*** external "ReactJSXRuntime" ***!
  \**********************************/
/***/ ((module) => {

module.exports = window["ReactJSXRuntime"];

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
/*!****************************!*\
  !*** ./react_app/index.js ***!
  \****************************/
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ "react-dom");
/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _components_App__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/App */ "./react_app/components/App.jsx");
/* harmony import */ var _assets_scss_main_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./assets/scss/main.scss */ "./react_app/assets/scss/main.scss");
/* harmony import */ var _components_context_data_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/context/data-context */ "./react_app/components/context/data-context.jsx");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react/jsx-runtime */ "react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__);




// import "./assets/scss/previews/main.scss"


document.addEventListener("DOMContentLoaded", function () {
  const body = document.getElementById("trustpilot-reviewskit-body");
  const root = (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createRoot)(body);
  root.render(/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_components_context_data_context__WEBPACK_IMPORTED_MODULE_4__.DataContextProvider, {
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_components_App__WEBPACK_IMPORTED_MODULE_2__["default"], {})
  }));
});
})();

/******/ })()
;
//# sourceMappingURL=trustpilot-reviewskit.core.min.js.map