(()=>{"use strict";const e=window.React,s=window.ReactDOM,{ajaxurl:i,nonce:r,plugin_root_url:t,data:a,count:n,last_updated:l,domain:c,original_domain:o}=window.trustpilot_reviewkit,d=e=>e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString(),m=e=>{const s=`stars-${(Math.round(2*e)/2).toFixed(1).replace(".","_")}.svg`;return t+"/assets/images/"+s},v=e=>e>=4.5?"Excellent":e>=4?"Great":e>=3?"Good":e>=2?"Average":"Poor",u=window.ReactJSXRuntime,p=(0,e.createContext)();function w({children:s}){const[i,r]=(0,e.useState)("reviews"),[t,n]=(0,e.useState)(a||{}),l={tab:i,setTab:r,data:t,setData:n};return(0,u.jsx)(p.Provider,{value:l,children:s})}function h(){return(0,e.useContext)(p)}const x=()=>{const{tab:e,setTab:s}=h();return(0,u.jsx)("nav",{children:(0,u.jsx)("ul",{children:_.map((i=>(0,u.jsx)("li",{className:e===i.tab?"active":"",onClick:()=>s(i.tab),children:i.label},i.label)))})})},_=[{label:"Reviews",tab:"reviews"},{label:"Shortcodes",tab:"shortcodes"}],j=window.wp.components,g=window.wp.i18n,N=({type:e="",...s})=>"light"===e?(0,u.jsx)("img",{src:t+"/assets/images/light-single-star.png",alt:"Trustpilot Logo",style:{width:"20px"}}):(0,u.jsx)("img",{src:t+"/assets/images/single-star.svg",alt:"Trustpilot Logo",...s}),f=({trustscore:e,reviews:s})=>{const i=a?.business_details||{},r=e||i.trustScore||4.8,t=s||i.numberOfReviews||347;return(0,u.jsxs)("div",{className:"reviewkit_fpln_mini preview",children:[(0,u.jsxs)("div",{className:"reviewkit_fpln_inner_top",children:[(0,u.jsx)(N,{}),(0,u.jsx)("span",{className:"place_name",children:(0,g.__)("Trustpilot","reviewkit")})]}),(0,u.jsx)("div",{className:"reviewkit_bg",children:(0,u.jsx)("img",{src:m(r),alt:`${r} star rating`})}),(0,u.jsxs)("div",{className:"reviewkit_fpln_inner_bottom",children:[(0,u.jsxs)("div",{className:"reviewkit_left_reviews",children:[(0,u.jsxs)("span",{className:"review_us_one",children:[(0,g.__)("TrustScore","reviewkit")," "]}),(0,u.jsx)("span",{className:"reviewkit_orignal_rcount",children:r})]}),(0,u.jsxs)("div",{className:"reviewkit_review_area",children:[(0,u.jsx)("span",{className:"reviewkit_out_of",children:d(t)}),(0,u.jsxs)("span",{className:"reviewkit_reviews",children:[" ",(0,g.__)("reviews","reviewkit")]})]})]})]})},b=({reviews:e})=>{const s=a?.business_details||{},i=e||s.numberOfReviews||1376,r=s.trustScore||4.8;return(0,u.jsxs)("div",{className:"reviewkit_fpln_starter preview",children:[(0,u.jsxs)("div",{className:"reviewkit_fpln_inner_top",children:[(0,u.jsxs)("span",{className:"mirc_see",children:[(0,g.__)("Check out our","reviewkit")," "]}),(0,u.jsx)("span",{className:"mirc_r_count",children:d(i)}),(0,u.jsxs)("span",{className:"review_us_one",children:[" ",(0,g.__)("reviews","reviewkit")]})]}),(0,u.jsx)("div",{className:"reviewkit_bg",children:(0,u.jsx)("img",{src:m(r),alt:`${r} star rating`})}),(0,u.jsx)("div",{className:"reviewkit_toltip_wrap",children:(0,u.jsxs)("div",{className:"reviewkit_logo_container",children:[(0,u.jsx)(k,{}),(0,u.jsx)("img",{src:t+"/assets/images/single-star.svg",alt:"Trustpilot Logo"}),(0,u.jsx)("span",{className:"reviewkit_text",children:"Trustpilot"})]})})]})},k=()=>(0,u.jsxs)("div",{className:"reviewkit_tooltip reviewkit_tooltip_large",children:[(0,u.jsx)("div",{className:"reviewkit_tooltip_content",children:(0,g.__)("Helping each other make better choices","reviewkit")}),(0,u.jsx)("a",{href:"#",className:"reviewkit_tooltip_link",children:(0,g.__)("Read and write reviews","reviewkit")})]}),y=({rating:e,score:s})=>{const i=s||(a?.business_details||{}).trustScore||4.8,r=e||(e=>e>=4.5?"Excellent":e>=4?"Great":e>=3?"Good":e>=2?"Average":"Poor")(i);return(0,u.jsxs)("div",{className:"reviewkit_fpln_mcts reviewkit_fpln_common preview",children:[(0,u.jsxs)("div",{className:"reviewkit_fpln_inner_left",children:[(0,u.jsx)("span",{className:"review_us_one",children:r}),(0,u.jsxs)("div",{className:"reviewkit_score",children:[(0,u.jsx)("span",{className:"reviewkit_orignal_rcount",children:i}),(0,u.jsx)("span",{className:"reviewkit_out_of",children:" out of 5"})]})]}),(0,u.jsxs)("div",{className:"reviewkit_fpln_inner_right",children:[(0,u.jsx)(N,{}),(0,u.jsx)("span",{className:"place_name",children:"Trustpilot"})]})]})},C=({rating:e,stars:s})=>{const i=(a?.business_details||{}).trustScore||4.8,r=e||v(i);return(0,u.jsxs)("div",{className:"reviewkit_fpln_mcs reviewkit_fpln_common preview",children:[(0,u.jsx)("div",{className:"dynamic_rating",children:r}),(0,u.jsx)("div",{className:"dynamic_stars",children:(0,u.jsx)("img",{src:m(i),alt:`${r} star rating`,style:{width:"100%"}})}),(0,u.jsxs)("div",{className:"tp-wrapper",children:[(0,u.jsx)(N,{}),(0,u.jsx)("div",{className:"place_name",children:(0,g.__)("Trustpilot","reviewkit")})]})]})},T=({count:e})=>{const s=e||(a?.business_details||{}).numberOfReviews||437;return(0,u.jsx)("div",{children:(0,u.jsxs)("a",{className:"reviewkit_fpln_mirc reviewkit_fpln_common preview",href:"#",children:[(0,u.jsx)("span",{className:"mirc_see",children:(0,g.__)("See our","reviewkit")}),(0,u.jsx)("span",{className:"mirc_r_count",children:d(s)}),(0,u.jsx)("span",{className:"review_us_one",children:(0,g.__)("reviews on","reviewkit")}),(0,u.jsx)(N,{style:{width:"20px"}}),(0,u.jsx)("span",{className:"place_name",children:(0,g.__)("Trustpilot","reviewkit")})]})})},S=({reviews:e})=>{const s=e||(a?.business_details||{}).numberOfReviews||1300;return(0,u.jsxs)("div",{className:"reviewkit_fpln_mcb_wrap",children:[(0,u.jsxs)("a",{className:"reviewkit_fpln_mcb_left",href:"#",children:[(0,u.jsx)(N,{type:"light"}),(0,u.jsx)("span",{className:"place_name",children:(0,g.__)("Trustpilot","reviewkit")})]}),(0,u.jsxs)("div",{className:"reviewkit_fpln_mcb_right",children:[d(s)," ",(0,g.__)("reviews","reviewkit")]})]})},R=({rating:e,stars:s,reviews:i})=>{const r=a?.business_details||{},t=r.trustScore||4.8,n=i||r.numberOfReviews||437,l=e||v(t);return(0,u.jsxs)("div",{className:"reviewkit_fpln_mc reviewkit_fpln_common preview",children:[(0,u.jsxs)("div",{className:"reviewkit_fpln_mc_inner_left",children:[(0,u.jsx)("span",{className:"review_us_one",children:l}),(0,u.jsx)("div",{className:"reviewkit_star_rating",children:(0,u.jsx)("img",{src:m(t),alt:`${l} star rating`,style:{width:"100%"}})})]}),(0,u.jsxs)("div",{className:"reviewkit_fpln_mc_inner_right",children:[(0,u.jsx)("span",{className:"mirc_r_count",children:d(n)}),(0,u.jsx)("span",{className:"review_us_one",children:(0,g.__)("reviews on","reviewkit")}),(0,u.jsx)(N,{style:{width:"18px"}}),(0,u.jsx)("span",{className:"place_name",children:(0,g.__)("Trustpilot","reviewkit")})]})]})},L=()=>{const[s,i]=(0,e.useState)(""),[r,t]=(0,e.useState)(""),a=[{id:"mini",name:"Mini Trustbox",shortcode:"[reviewkit_trustpilot_mini]",description:"Compact widget showing TrustScore, star rating, and review count",category:"free",preview:f,attributes:[{name:"trustscore",default:"4.8",description:"Trust score rating (0-5)"},{name:"reviews",default:"347",description:"Number of reviews"},{name:"url",default:"#",description:"Link URL"}]},{id:"starter",name:"Starter Trustbox",shortcode:"[reviewkit_trustpilot_starter]",description:"Interactive widget with star rating and tooltip",category:"free",preview:b,attributes:[{name:"reviews",default:"1,376",description:"Number of reviews"},{name:"url",default:"#",description:"Link URL"}]},{id:"micro_trustscore",name:"Micro TrustScore",shortcode:"[reviewkit_trustpilot_micro_trustscore]",description:"Simple display of TrustScore rating",category:"free",preview:y,attributes:[{name:"rating",default:"Excellent",description:"Rating text"},{name:"score",default:"4.8",description:"Numeric score"},{name:"url",default:"#",description:"Link URL"}]},{id:"micro_star",name:"Micro Star",shortcode:"[reviewkit_trustpilot_micro_star]",description:"Interactive star rating display",category:"free",preview:C,attributes:[{name:"rating",default:"Excellent",description:"Rating text"},{name:"stars",default:"5",description:"Number of stars (1-5)"},{name:"url",default:"#",description:"Link URL"}]},{id:"micro_reviewcount",name:"Micro Review Count",shortcode:"[reviewkit_trustpilot_micro_reviewcount]",description:"Link showing review count",category:"free",preview:T,attributes:[{name:"count",default:"437",description:"Number of reviews"},{name:"url",default:"#",description:"Link URL"}]},{id:"micro_button",name:"Micro Button",shortcode:"[reviewkit_trustpilot_micro_button]",description:"Button-style widget with review count",category:"free",preview:S,attributes:[{name:"reviews",default:"1.3K+",description:"Review count display"},{name:"url",default:"#",description:"Link URL"}]},{id:"micro_combo",name:"Micro Combo",shortcode:"[reviewkit_trustpilot_micro_combo]",description:"Combined star rating and review count widget",category:"free",preview:R,attributes:[{name:"rating",default:"Excellent",description:"Rating text"},{name:"stars",default:"5",description:"Number of stars (1-5)"},{name:"reviews",default:"437",description:"Number of reviews"},{name:"url",default:"#",description:"Link URL"}]}].filter((e=>e.name.toLowerCase().includes(s.toLowerCase())||e.description.toLowerCase().includes(s.toLowerCase())||e.shortcode.toLowerCase().includes(s.toLowerCase())));return(0,u.jsxs)("div",{className:"page shortcodes-page",children:[(0,u.jsxs)("div",{className:"shortcodes-header",style:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,u.jsxs)("div",{children:[(0,u.jsx)("h2",{children:(0,g.__)("Trustpilot Shortcodes","trustpilot-reviewkit")}),(0,u.jsx)("p",{children:(0,g.__)("Use these shortcodes to display Trustpilot widgets on your website.","trustpilot-reviewkit")})]}),(0,u.jsx)(j.TextControl,{value:s,onChange:i,placeholder:(0,g.__)("Search by name, description, or shortcode...","trustpilot-reviewkit"),className:"shortcodes-search"})]}),(0,u.jsx)("div",{className:"shortcodes-grid",children:a.map((e=>(0,u.jsxs)(j.Card,{className:`shortcode-card ${e.id}`,children:[(0,u.jsxs)(j.CardHeader,{children:[(0,u.jsx)("h3",{children:e.name}),(0,u.jsx)("span",{className:"shortcode-category",children:e.category})]}),(0,u.jsxs)(j.CardBody,{children:[(0,u.jsx)("div",{className:"shortcode-preview",children:e.preview?(0,u.jsx)("div",{className:"preview-component",style:{overflow:"visible"},children:(0,u.jsx)(e.preview,{})}):(0,u.jsxs)("div",{className:"preview-placeholder",children:[(0,u.jsx)("span",{children:"📊"}),(0,u.jsx)("small",{children:(0,g.__)("Preview","trustpilot-reviewkit")})]})}),(0,u.jsx)("p",{className:"shortcode-description",children:e.description}),(0,u.jsxs)("div",{className:"shortcode-code",children:[(0,u.jsx)("code",{children:e.shortcode}),(0,u.jsx)(j.Button,{variant:"secondary",size:"small",onClick:()=>(async e=>{try{if(navigator.clipboard&&window.isSecureContext)return await navigator.clipboard.writeText(e),t(e),void setTimeout((()=>t("")),2e3);const s=document.createElement("textarea");s.value=e,s.style.position="fixed",s.style.left="-999999px",s.style.top="-999999px",document.body.appendChild(s),s.focus(),s.select();const i=document.execCommand("copy");if(document.body.removeChild(s),!i)throw new Error("execCommand failed");t(e),setTimeout((()=>t("")),2e3)}catch(s){console.error("Failed to copy shortcode: ",s),alert(`Copy this shortcode manually: ${e}`)}})(e.shortcode),className:r===e.shortcode?"copied":"",children:r===e.shortcode?(0,g.__)("Copied!","trustpilot-reviewkit"):(0,g.__)("Copy","trustpilot-reviewkit")})]})]})]},e.id)))}),0===a.length&&(0,u.jsx)("div",{className:"no-results",children:(0,u.jsx)("p",{children:(0,g.__)("No shortcodes found matching your search.","trustpilot-reviewkit")})})]})},E=({businessDetails:e={}})=>{const{displayName:s="",identifyingName:i="",numberOfReviews:r=0,trustScore:t=0,websiteUrl:a="",profileImageUrl:n="",stars:l=0,categories:c=[],activity:o={}}=e,d=c.find((e=>e.isPrimary))?.name||"",p=o?.verification?.verifiedUserIdentity||!1,w=o?.isClaimed||!1,h=v(t);return s?(0,u.jsxs)("div",{className:"trustpilot-business-page",children:[(0,u.jsxs)("div",{className:"business-header",children:[(0,u.jsxs)("div",{className:"business-logo",children:[n?(0,u.jsx)("img",{src:(_=n,_?_.startsWith("//")?`https:${_}`:_:""),alt:`${s} logo`,className:"business-image",onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="flex"}}):null,(0,u.jsx)("div",{className:"business-initials",style:{display:n?"none":"flex"},children:s.charAt(0).toUpperCase()})]}),(0,u.jsxs)("div",{className:"business-info",children:[(0,u.jsxs)("div",{className:"business-name-section",children:[(0,u.jsx)("h2",{className:"business-name",children:s}),w&&(0,u.jsxs)("span",{className:"business-claimed",children:["✓ ",(0,g.__)("Claimed","reviewkit")]}),p&&(0,u.jsxs)("span",{className:"business-verified",children:["✓ ",(0,g.__)("Verified","reviewkit")]})]}),i&&(0,u.jsx)("p",{className:"business-website",children:i}),d&&(0,u.jsx)("p",{className:"business-category",children:d}),(0,u.jsxs)("div",{className:"business-rating",children:[(0,u.jsxs)("div",{className:"rating-percentage",children:[(0,u.jsx)("div",{className:"dynamic_stars",style:{width:"200px"},children:(0,u.jsx)("img",{src:m(t),alt:`${h} star rating`})}),(0,u.jsx)("span",{children:t})]}),(0,u.jsxs)("span",{className:"review-count",children:[(0,g.__)("Based on","reviewkit")," ",(x=r,x>=1e6?(x/1e6).toFixed(1)+"M":x>=1e3?(x/1e3).toFixed(1)+"K":x.toString())," ",(0,g.__)("reviews","reviewkit")]})]}),a&&(0,u.jsx)("div",{className:"business-links",children:(0,u.jsx)("a",{href:a,target:"_blank",rel:"noopener noreferrer",className:"components-button is-secondary business-website-link",children:(0,g.__)("Visit Website","reviewkit")})})]})]}),o?.replyBehavior&&(0,u.jsxs)("div",{className:"business-stats",children:[(0,u.jsxs)("div",{className:"stat-item",children:[(0,u.jsxs)("span",{className:"stat-label",children:[(0,g.__)("Reply Rate","reviewkit"),":"]}),(0,u.jsxs)("span",{className:"stat-value",children:[o.replyBehavior.replyPercentage,"%"]})]}),o.replyBehavior.averageDaysToReply&&(0,u.jsxs)("div",{className:"stat-item",children:[(0,u.jsxs)("span",{className:"stat-label",children:[(0,g.__)("Avg. Reply Time","reviewkit"),":"]}),(0,u.jsxs)("span",{className:"stat-value",children:[Math.round(o.replyBehavior.averageDaysToReply)," ",(0,g.__)("days","reviewkit")]})]})]})]}):(0,u.jsx)("div",{className:"trustpilot-business-page",children:(0,u.jsx)("div",{className:"business-placeholder",children:(0,u.jsx)("p",{children:(0,g.__)("No business details found","reviewkit")})})});var x,_},D=({customer:e={},size:s="medium",showImage:i=!0})=>{const{name:r="Anonymous",image:t=""}=e;return(0,u.jsxs)("div",{className:`customer-info customer-info--${s}`,children:[i&&(0,u.jsxs)("div",{className:"customer-avatar",children:[t?(0,u.jsx)("img",{src:t,alt:`${r}'s profile`,className:"customer-image",onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="flex"}}):null,(0,u.jsx)("div",{className:"customer-initials",style:{display:t?"none":"flex"},children:(e=>e&&"Customer"!==e?e.split(" ").map((e=>e.charAt(0))).join("").toUpperCase().slice(0,2):"?")(r)})]}),(0,u.jsx)("div",{className:"customer-details",children:(0,u.jsx)("span",{className:"customer-name",children:r})})]})},U=({review:e={}})=>{const{reviewId:s,rating:i=0,reviewTitle:r="",reviewBody:t="",customer:a={},dates:n={},reviewUrl:l=""}=e;console.info(e);const{publishedDate:c,experiencedDate:o}=n,d=e=>{if(!e)return"";try{return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}catch(e){return""}},p=v(i);return(0,u.jsxs)("div",{className:"review-card","data-review-id":s,children:[(0,u.jsxs)("div",{className:"review-header",children:[(0,u.jsx)(D,{customer:a,size:"small"}),(0,u.jsxs)("div",{className:"review-meta",children:[(0,u.jsx)("div",{className:"dynamic_stars",style:{width:"150px"},children:(0,u.jsx)("img",{src:m(e.rating),alt:`${p} star rating`,style:{width:"100%"}})}),c&&(0,u.jsx)("span",{className:"review-date",children:d(c)})]})]}),(0,u.jsxs)("div",{className:"review-content",children:[r&&(0,u.jsx)("h4",{className:"review-title",children:r}),t&&(0,u.jsx)("p",{className:"review-body",children:((e,s=500)=>!e||e.length<=s?e:e.substring(0,s)+"...")(t)}),o&&(0,u.jsx)("div",{className:"review-experience-date",children:(0,u.jsxs)("small",{children:["Experience date: ",d(o)]})})]}),l&&(0,u.jsx)("div",{className:"review-footer",children:(0,u.jsx)("a",{href:l,target:"_blank",rel:"noopener noreferrer",className:"review-link",children:"View on Trustpilot"})})]})},M=({reviews:s={},initialDisplayCount:i=10,showLoadMore:r=!0})=>{const[t,a]=(0,e.useState)(i),n=Object.values(s).sort(((e,s)=>{const i=new Date(e.dates?.publishedDate||0);return new Date(s.dates?.publishedDate||0)-i})),l=n.length,c=n.slice(0,t),o=t<l,[d,m]=(0,e.useState)(null),v=d?c.filter((e=>e.rating===d)):c,p=(()=>{const e={1:0,2:0,3:0,4:0,5:0};return n.forEach((s=>{s.rating>=1&&s.rating<=5&&e[Math.floor(s.rating)]++})),e})();return 0===l?(0,u.jsx)("div",{className:"review-list",children:(0,u.jsx)("div",{className:"review-list-empty",children:(0,u.jsx)("p",{children:(0,g.__)(">No reviews available","reviewkit")})})}):(0,u.jsxs)("div",{className:"review-list",children:[(0,u.jsxs)("div",{className:"review-list-header",children:[(0,u.jsxs)("h3",{className:"review-list-title",children:["Customer Reviews (",l,")"]}),(0,u.jsx)("div",{className:"review-filters",children:(0,u.jsxs)("div",{className:"rating-filter",children:[(0,u.jsx)("button",{className:"filter-btn "+(null===d?"active":""),onClick:()=>m(null),children:"All"}),[5,4,3,2,1].map((e=>(0,u.jsxs)("button",{className:"filter-btn "+(d===e?"active":""),onClick:()=>m(e),children:[e,"★ (",p[e],")"]},e)))]})})]}),(0,u.jsx)("div",{className:"review-list-content",children:0===v.length?(0,u.jsx)("div",{className:"review-list-empty",children:(0,u.jsx)("p",{children:"No reviews found for the selected rating"})}):(0,u.jsx)("div",{className:"review-cards",children:v.map((e=>(0,u.jsx)(U,{review:e},e.reviewId)))})}),r&&null===d&&(0,u.jsxs)("div",{className:"review-list-controls",children:[o&&(0,u.jsxs)("button",{className:"load-more-btn",onClick:()=>{a((e=>Math.min(e+i,l)))},children:["Load More Reviews (",l-t," remaining)"]}),t>i&&(0,u.jsx)("button",{className:"show-less-btn",onClick:()=>{a(i)},children:"Show Less"})]}),(0,u.jsx)("div",{className:"review-summary",children:(0,u.jsxs)("p",{className:"review-count-summary",children:["Showing ",v.length," of ",l," reviews"]})})]})},$=()=>{const{data:s={},setData:i}=h(),{business_details:t={},reviews:a=[]}=s,[n,l]=(0,e.useState)(o),[c,d]=(0,e.useState)(!1),m=async(e=!1)=>{d(!0);const s=await((e,s={})=>{if(!e)return Promise.reject(new Error("Domain is required"));const{timeout:i=3e4}=s;return void 0===window.ajaxurl||void 0===window.trustpilot_reviewkit?Promise.reject(new Error("WordPress AJAX not available. Make sure the script is properly enqueued.")):new Promise(((t,a)=>{const n=new FormData;if(n.append("action","trustpilot_reviewkit_get_reviews"),n.append("security",r),n.append("domain",e),n.append("revalidate",s.revalidate),window.jQuery)jQuery.ajax({url:window.ajaxurl,type:"POST",data:n,processData:!1,contentType:!1,timeout:i,success:function(e){e.success?t(e.data):a(new Error(e.data?.message||"Unknown error occurred"))},error:function(e,s,i){a("timeout"===s?new Error("Request timed out"):new Error(`AJAX request failed: ${i}`))}});else{const e=new AbortController,s=setTimeout((()=>e.abort()),i);fetch(window.ajaxurl,{method:"POST",body:n,signal:e.signal}).then((e=>{if(clearTimeout(s),!e.ok)throw new Error(`Server returned ${e.status}: ${e.statusText}`);return e.json()})).then((e=>{e.success?t(e.data):a(new Error(e.data?.message||"Unknown error occurred"))})).catch((e=>{clearTimeout(s),"AbortError"===e.name?a(new Error("Request timed out")):a(e)}))}}))})(n,{revalidate:e});s?.data&&i(s.data),d(!1)};return(0,u.jsxs)("div",{className:"page review-page",children:[(0,u.jsxs)("div",{className:"review-fetch",children:[(0,u.jsx)(j.TextControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,label:(0,g.__)("Trustpilot URL","trustpilot-reviewkit"),type:"url",help:(0,g.__)("Enter the URL of your Trustpilot page","trustpilot-reviewkit"),value:n,onChange:e=>l(e)}),(0,u.jsxs)("div",{className:"button-container",children:[(0,u.jsx)(j.Button,{variant:"primary",style:{backgroundColor:"#28a745",fontWeight:"600"},onClick:m,disabled:""===n,children:(0,g.__)("Fetch Reviews","trustpilot-reviewkit")}),(0,u.jsx)(j.Button,{variant:"secondary",onClick:()=>m(!0),disabled:!s,children:(0,g.__)("Revalidate Reviews","trustpilot-reviewkit")})]})]}),c?(0,u.jsx)("div",{style:{marginTop:"20px",textAlign:"center"},children:(0,u.jsx)(j.Spinner,{style:{height:"calc(4px * 10)",width:"calc(4px * 10)"}})}):(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(E,{businessDetails:t}),(0,u.jsx)(M,{reviews:a})]})]})},A=()=>{const{tab:e}=h();return(0,u.jsxs)("div",{children:[(0,u.jsx)(x,{}),"reviews"===e&&(0,u.jsx)($,{}),"shortcodes"===e&&(0,u.jsx)(L,{})]})};document.addEventListener("DOMContentLoaded",(function(){const e=document.getElementById("trustpilot-reviewskit-body");(0,s.createRoot)(e).render((0,u.jsx)(w,{children:(0,u.jsx)(A,{})}))}))})();