/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/css-loader@6.11.0_webpack@5.101.0/node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/.pnpm/postcss-loader@6.2.1_postcss@8.5.6_webpack@5.101.0/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./node_modules/.pnpm/sass-loader@16.0.5_sass@1.89.2_webpack@5.101.0/node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[3]!./react_app/assets/scss/previews/main.scss ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.reviewkit_fpln_common {
  display: inline-flex;
  gap: 5px;
  padding: 22px 26px;
  border-radius: 4px;
  border: 1px solid #00b67a;
  text-decoration: none;
  font-size: 16px;
  font-weight: 600;
  color: #14223b;
}
.reviewkit_fpln_common.preview {
  font-size: 12px;
  display: flex;
  align-items: center;
}

.reviewkit_fpln_star_icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  fill: #00b67a;
}

/* Star Rating Container */
.reviewkit_star_rating {
  display: inline-flex;
  flex-direction: row-reverse;
  justify-content: start;
  gap: 5px;
  margin-bottom: 15px;
}

/* Hide radio buttons */
.reviewkit_star_rating input {
  display: none;
}

/* Style star labels */
.reviewkit_star_rating label {
  font-size: 40px;
  line-height: 1;
  color: #ddd;
  cursor: pointer;
  transition: all 0.3s ease;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

/* Hover effect */
.reviewkit_star_rating label:hover {
  transform: scale(1.1);
}

/* Default hover color (yellow) */
.reviewkit_star_rating label:hover,
.reviewkit_star_rating label:hover ~ label {
  color: #ffce00;
}

/* 1 Star Selected - RED */
.reviewkit_star_rating input[value="1"]:checked ~ label[for=star1] {
  color: #ff3722;
}

/* 2 Stars Selected - ORANGE */
.reviewkit_star_rating input[value="2"]:checked ~ label[for=star2],
.reviewkit_star_rating input[value="2"]:checked ~ label[for=star1] {
  color: #ff8622;
}

/* 3 Stars Selected - YELLOW */
.reviewkit_star_rating input[value="3"]:checked ~ label[for=star3],
.reviewkit_star_rating input[value="3"]:checked ~ label[for=star2],
.reviewkit_star_rating input[value="3"]:checked ~ label[for=star1] {
  color: #ffce00;
}

/* 4 Stars Selected - BLUE */
.reviewkit_star_rating input[value="4"]:checked ~ label[for=star4],
.reviewkit_star_rating input[value="4"]:checked ~ label[for=star3],
.reviewkit_star_rating input[value="4"]:checked ~ label[for=star2],
.reviewkit_star_rating input[value="4"]:checked ~ label[for=star1] {
  color: #73cf11;
}

/* 5 Stars Selected - GREEN */
.reviewkit_star_rating input[value="5"]:checked ~ label[for=star5],
.reviewkit_star_rating input[value="5"]:checked ~ label[for=star4],
.reviewkit_star_rating input[value="5"]:checked ~ label[for=star3],
.reviewkit_star_rating input[value="5"]:checked ~ label[for=star2],
.reviewkit_star_rating input[value="5"]:checked ~ label[for=star1] {
  color: #00b67a;
}

/* Override hover when rating is selected */
.reviewkit_star_rating input[value="1"]:checked ~ label:hover,
.reviewkit_star_rating input[value="1"]:checked ~ label:hover ~ label {
  color: #ff3722;
}

.reviewkit_star_rating input[value="2"]:checked ~ label:hover,
.reviewkit_star_rating input[value="2"]:checked ~ label:hover ~ label {
  color: #ff8622;
}

.reviewkit_star_rating input[value="3"]:checked ~ label:hover,
.reviewkit_star_rating input[value="3"]:checked ~ label:hover ~ label {
  color: #ffce00;
}

.reviewkit_star_rating input[value="4"]:checked ~ label:hover,
.reviewkit_star_rating input[value="4"]:checked ~ label:hover ~ label {
  color: #73cf11;
}

.reviewkit_star_rating input[value="5"]:checked ~ label:hover,
.reviewkit_star_rating input[value="5"]:checked ~ label:hover ~ label {
  color: #00b67a;
}

.reviewkit_fpln_mirc .mirc_see {
  font-weight: 500;
}
.reviewkit_fpln_mirc .mirc_r_count {
  font-weight: 700;
}
.reviewkit_fpln_mirc .review_us_one {
  font-weight: 500;
}
.reviewkit_fpln_mirc .place_name {
  font-weight: 600;
}

.reviewkit_fpln_mcb_wrap {
  display: flex;
}
.reviewkit_fpln_mcb_wrap .reviewkit_fpln_mcb_left {
  background-color: #00b67a;
  color: #fff;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  text-decoration: none;
}
.reviewkit_fpln_mcb_wrap .reviewkit_fpln_mcb_left .reviewkit_fpln_star_icon {
  fill: #fff;
}
.reviewkit_fpln_mcb_wrap .reviewkit_fpln_mcb_left .place_name {
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  line-height: 1;
}
.reviewkit_fpln_mcb_wrap .reviewkit_fpln_mcb_right {
  display: flex;
  border: 1px solid #cfd0cf;
  align-items: center;
  background-color: #fff;
  font-size: 16px;
  line-height: 1;
  color: #555;
  padding: 6px 10px;
}

.reviewkit_fpln_mc {
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}
.reviewkit_fpln_mc .reviewkit_fpln_mc_inner_left {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}
.reviewkit_fpln_mc .reviewkit_fpln_mc_inner_left .review_us_one {
  font-weight: 700;
}
.reviewkit_fpln_mc .reviewkit_fpln_mc_inner_left .reviewkit_star_rating {
  display: inline-flex;
  margin-bottom: 0;
  width: 150px;
}
.reviewkit_fpln_mc .reviewkit_fpln_mc_inner_left .reviewkit_star_rating img {
  width: 100%;
}
.reviewkit_fpln_mc .reviewkit_fpln_mc_inner_left .reviewkit_star_rating label {
  font-size: 16px;
  padding: 4px;
  color: #fff;
  background: #00b67a;
}
.reviewkit_fpln_mc .reviewkit_fpln_mc_inner_right {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}
.reviewkit_fpln_mc .reviewkit_fpln_mc_inner_right .review_us_one {
  font-weight: 400;
}
.reviewkit_fpln_mc .reviewkit_fpln_mc_inner_right .reviewkit_fpln_star_icon {
  margin-right: 0;
}

.reviewkit_fpln_mcs.frontend .dynamic_rating, .reviewkit_fpln_mcs.frontend .place_name {
  font-weight: 600;
  font-size: 18px;
}
.reviewkit_fpln_mcs.frontend .dynamic_stars {
  width: 150px;
}
.reviewkit_fpln_mcs .dynamic_rating, .reviewkit_fpln_mcs .place_name {
  font-weight: 600;
  font-size: 14px;
}
.reviewkit_fpln_mcs .dynamic_stars {
  width: 100px;
  line-height: 0;
}
.reviewkit_fpln_mcs > .tp-wrapper {
  display: flex;
  align-items: center;
  -moz-column-gap: 5px;
       column-gap: 5px;
}
.reviewkit_fpln_mcs > .tp-wrapper img {
  width: 20px;
}

.reviewkit_fpln_mcs {
  align-items: center;
  gap: 15px;
}
.reviewkit_fpln_mcs .reviewkit_fpln_mc_inner_left {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}
.reviewkit_fpln_mcs .reviewkit_fpln_mc_inner_left .review_us_one {
  font-weight: 700;
}
.reviewkit_fpln_mcs .reviewkit_fpln_mc_inner_left .reviewkit_star_rating {
  display: inline-flex;
  margin-bottom: 0;
}
.reviewkit_fpln_mcs .reviewkit_fpln_mc_inner_left .reviewkit_star_rating label {
  font-size: 26px;
  color: #00b67a;
}
.reviewkit_fpln_mcs .reviewkit_fpln_mc_inner_right .review_us_one {
  font-weight: 400;
}
.reviewkit_fpln_mcs .reviewkit_fpln_mc_inner_right .reviewkit_fpln_star_icon {
  margin-right: 0;
}

.reviewkit_fpln_mcts {
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}
.reviewkit_fpln_mcts .reviewkit_fpln_inner_left {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}
.reviewkit_fpln_mcts .reviewkit_fpln_inner_left .reviewkit_out_of {
  font-weight: 400;
  word-spacing: 1px;
}
.reviewkit_fpln_mcts .reviewkit_fpln_inner_right {
  display: flex;
  align-items: center;
  -moz-column-gap: 5px;
       column-gap: 5px;
}
.reviewkit_fpln_mcts .reviewkit_fpln_inner_right .reviewkit_fpln_star_icon {
  margin-right: 0;
}

.reviewkit_fpln_mcts.preview .reviewkit_fpln_inner_right {
  display: flex;
  align-items: center;
  -moz-column-gap: 3px;
       column-gap: 3px;
}
.reviewkit_fpln_mcts.preview .reviewkit_fpln_inner_right img {
  width: 15px;
}

.reviewkit_fpln_mini.preview .reviewkit_fpln_inner_top .place_name {
  font-size: 24px;
}

.reviewkit_fpln_mini {
  width: 300px;
  display: inline-flex;
  flex-direction: column;
}
.reviewkit_fpln_mini .reviewkit_fpln_inner_top {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  -moz-column-gap: 3px;
       column-gap: 3px;
}
.reviewkit_fpln_mini .reviewkit_fpln_inner_top .reviewkit_fpln_star_icon {
  margin-right: 0;
}
.reviewkit_fpln_mini .reviewkit_fpln_inner_top > span {
  font-weight: bold;
}
.reviewkit_fpln_mini .reviewkit_fpln_inner_top img {
  width: 30px;
}
.reviewkit_fpln_mini .reviewkit_bg img {
  width: 100%;
  margin-bottom: 4px;
}
.reviewkit_fpln_mini .reviewkit_fpln_inner_bottom {
  display: flex;
  gap: 20px;
  font-size: 1.2rem;
}
.reviewkit_fpln_mini .reviewkit_fpln_inner_bottom .reviewkit_left_reviews {
  position: relative;
}
.reviewkit_fpln_mini .reviewkit_fpln_inner_bottom .reviewkit_left_reviews::before {
  position: absolute;
  content: "";
  top: 50%;
  right: -10px;
  height: 14px;
  transform: translateY(-50%);
  width: 1px;
  background: #14223b;
}
.reviewkit_fpln_mini .reviewkit_fpln_inner_bottom .reviewkit_left_reviews .reviewkit_orignal_rcount {
  font-weight: 600;
}
.reviewkit_fpln_mini .reviewkit_fpln_inner_bottom .reviewkit_review_area .reviewkit_out_of {
  font-weight: 600;
}
.reviewkit_fpln_starter.preview .reviewkit_toltip_wrap .reviewkit_logo_container img {
  width: 20px;
}

.reviewkit_fpln_starter {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.reviewkit_fpln_starter .reviewkit_fpln_inner_top {
  margin-bottom: 6px;
}
.reviewkit_fpln_starter .reviewkit_fpln_inner_top .mirc_r_count {
  font-weight: 600;
}
.reviewkit_fpln_starter .reviewkit_bg img {
  width: 150px;
  margin-bottom: 4px;
}
.reviewkit_fpln_starter .reviewkit_toltip_wrap {
  display: flex;
  align-items: center;
}
.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}
.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container .reviewkit_star {
  width: 20px;
  height: 20px;
  background-color: #00b67a;
  clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
  transition: transform 0.2s ease;
}
.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container .reviewkit_text {
  font-size: 18px;
  font-weight: 600;
  color: #14223b;
  letter-spacing: -0.5px;
}
.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container:hover .reviewkit_star {
  transform: scale(1.1);
}
.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container .reviewkit_tooltip {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #14223b;
  color: #fff;
  padding: 16px 20px;
  border-radius: 8px;
  font-size: 14px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 10;
  margin-bottom: 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  text-align: center;
}
.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container .reviewkit_tooltip::after {
  content: "";
  position: absolute;
  top: -16px;
  left: 50%;
  transform: translateX(-50%);
  border: 8px solid transparent;
  border-bottom-color: #14223b;
}
.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container .reviewkit_tooltip .reviewkit_tooltip_content {
  margin-bottom: 6px;
}
.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container:hover .reviewkit_tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(7px);
}
.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container .reviewkit_tooltip_link {
  color: #00b67a;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}
.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container .reviewkit_tooltip_link:hover {
  text-decoration: underline;
}

.reviewkit_header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}
.reviewkit_header img {
  width: 150px;
}
.reviewkit_header .reviewkit_profile_name {
  margin: 0;
}

.reviewkit_profile_review {
  position: relative;
  display: flex;
  gap: 5px;
  align-items: center;
  margin-bottom: 15px;
}
.reviewkit_profile_review .reviewkit_review_secore {
  font-weight: 700;
}
.reviewkit_profile_review .reviewkit_review_img img {
  width: 150px;
}

.reviewkit_prium_grid_layout {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.reviewkit_prium_grid__single_item {
  padding: 15px;
  border-radius: 6px;
  background: #fafafa;
}
.reviewkit_prium_grid__single_item .reviewkit_ratting_author_time_wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}
.reviewkit_prium_grid__single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_author_left {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 15px;
}
.reviewkit_prium_grid__single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_author_left .reviewkit_star_rating {
  margin-bottom: 0;
}
.reviewkit_prium_grid__single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_author_left .reviewkit_star_rating label {
  font-size: 30px;
}
.reviewkit_prium_grid__single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_author_left .reviewkit_ratting_author {
  color: #98999a;
}
.reviewkit_prium_grid__single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_author_left .reviewkit_ratting_author span {
  color: #484849;
}
.reviewkit_prium_grid__single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_time_right P {
  margin: 0;
  color: #98999a;
}
.reviewkit_prium_grid__single_item .reviewkit_title {
  font-size: 20px;
  font-weight: 600;
  line-height: 1;
  margin: 0 0 15px;
}
.reviewkit_prium_grid__single_item .reviewkit_pra {
  font-size: 16px;
  line-height: 24px;
  margin: 0;
}

.reviewkit_prium_header {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  margin-bottom: 30px;
}
.reviewkit_prium_header .reviewkit_prim_top {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}
@media screen and (max-width: 450px) {
  .reviewkit_prium_header .reviewkit_prim_top {
    gap: 10px;
  }
}
.reviewkit_prium_header .reviewkit_prim_top .reviewkit_profile_name {
  margin: 0;
}
.reviewkit_prium_header .reviewkit_prim_top .reviewkit_rating_bg {
  width: 150px;
}
.reviewkit_prium_header .reviewkit_prim_bottom .reviewkit_prim_r_count {
  font-weight: 600;
}
.reviewkit_prium_header .reviewkit_prim_bottom .place_name {
  font-weight: 600;
}

.reviewkit_prium_list_layout .reviewkit_prium_list_single_item {
  padding: 15px;
  border-radius: 6px;
  background-color: #fafafa;
  margin-bottom: 30px;
}
.reviewkit_prium_list_layout .reviewkit_prium_list_single_item .reviewkit_ratting_author_time_wrap {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.reviewkit_prium_list_layout .reviewkit_prium_list_single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_author_left {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}
.reviewkit_prium_list_layout .reviewkit_prium_list_single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_author_left .reviewkit_star_rating {
  margin-bottom: 0;
}
.reviewkit_prium_list_layout .reviewkit_prium_list_single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_author_left .reviewkit_star_rating label {
  font-size: 30px;
}
.reviewkit_prium_list_layout .reviewkit_prium_list_single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_time_right P {
  margin: 0;
  color: #98999a;
}
.reviewkit_prium_list_layout .reviewkit_prium_list_single_item .reviewkit_title {
  font-size: 20px;
  font-weight: 600;
  line-height: 1;
  margin: 0 0 15px;
}
.reviewkit_prium_list_layout .reviewkit_prium_list_single_item .reviewkit_pra {
  font-size: 16px;
  line-height: 24px;
  margin: 0;
}

/*# sourceMappingURL=index.min.css.map*/