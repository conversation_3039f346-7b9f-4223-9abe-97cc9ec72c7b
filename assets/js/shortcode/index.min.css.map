{"version": 3, "file": "./shortcode/index.min.css", "mappings": ";;;AAGA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,cCZc;ACUlB;AFII;EACI;EACA;EACA;AEFR;;AFMA;EACI;EACA;EACA;EACA,aCvBe;ACoBnB;;AFOA;AACA;EACI;EACA;EACA;EACA;EACA;AEJJ;;AFOA;AACA;EACI;AEJJ;;AFOA;AACA;EACI;EACA;EACA;EACA;EACA;EACA;KAAA;UAAA;AEJJ;;AFOA;AACA;EACI;AEJJ;;AFOA;AACA;;EAEI;AEJJ;;AFOA;AACA;EACI;AEJJ;;AFOA;AACA;;EAEI;AEJJ;;AFOA;AACA;;;EAGI;AEJJ;;AFOA;AACA;;;;EAII;AEJJ;;AFOA;AACA;;;;;EAKI;AEJJ;;AFOA;AACA;;EAEI;AEJJ;;AFOA;;EAEI;AEJJ;;AFOA;;EAEI;AEJJ;;AFOA;;EAEI;AEJJ;;AFOA;;EAEI;AEJJ;;AClHI;EACI;ADqHR;AClHI;EACI;ADoHR;ACjHI;EACI;ADmHR;AC9GI;EACI;ADgHR;;AElIA;EAEI;AFoIJ;AElII;EACI;EACA;EACA;EACA;EACA;EACA;EACA;AFoIR;AElIQ;EACI;AFoIZ;AEjIQ;EACI;EACA;EACA;EACA;AFmIZ;AE/HI;EAGI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AF+HR;;AGjKA;EACI;EACA;EACA;AHoKJ;AGlKI;EACI;EACA;EACA;EACA;AHoKR;AGlKQ;EACI;AHoKZ;AGjKQ;EACI;EACA;EACA;AHmKZ;AGjKY;EACI;AHmKhB;AGhKY;EACI;EACA;EACA,WJ1BF;EI2BE,mBJ5BG;AC8LnB;AG7JI;EACI;EACA;EACA;AH+JR;AG7JQ;EACI;AH+JZ;AG5JQ;EACI;AH8JZ;;AItMQ;EACI;EACA;AJyMZ;AIvMQ;EACI;AJyMZ;AIrMI;EACI;EACA;AJuMR;AIpMI;EACI;EACA;AJsMR;AInMI;EACI;EACA;EACA;OAAA;AJqMR;AIpMS;EACG;AJsMZ;;AIhMA;EACI;EAEA;AJkMJ;AIhMI;EACI;EACA;EACA;EACA;AJkMR;AIhMQ;EACI;AJkMZ;AI/LQ;EACI;EACA;AJiMZ;AI/LY;EACI;EACA,cLtDG;ACuPnB;AI3LQ;EACI;AJ6LZ;AI1LQ;EACI;AJ4LZ;;AK/PA;EACI;EACA;EACA;ALkQJ;AKhQI;EACI;EACA;EACA;ALkQR;AKhQQ;EACI;EACA;ALkQZ;AK9PI;EACI;EACA;EACA;OAAA;ALgQR;AK/PQ;EACI;ALiQZ;;AK3PI;EACI;EACA;EACA;OAAA;AL8PR;AK7PQ;EACI;AL+PZ;;AMzRY;EACI;AN4RhB;;AMtRA;EACI;EACA;EAEA;ANwRJ;AMtRI;EACI;EACA;EACA;EACA;OAAA;ANwRR;AMtRQ;EACI;ANwRZ;AMrRQ;EACI;ANuRZ;AMpRQ;EACI;ANsRZ;AMhRQ;EACI;EACA;ANkRZ;AM9QI;EACI;EACA;EACA;ANgRR;AM9QQ;EACI;ANgRZ;AM9QY;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA,mBP9DE;AC8UlB;AM3QY;EACI;AN6QhB;AMxQY;EACI;AN0QhB;AO/UY;EACI;APiVhB;;AO3UA;EACI;EACA;EACA;AP8UJ;AO5UI;EACI;AP8UR;AO1UQ;EACI;AP4UZ;AOrUQ;EACI;EACA;APuUZ;AOnUI;EACI;EACA;APqUR;AOnUQ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;APqUZ;AOpUY;EACI;EACA;EACA,yBRjDG;EQkDH;EACA;APsUhB;AOnUY;EACI;EACA;EACA,cR3DE;EQ4DF;APqUhB;AOlUY;EACI;APoUhB;AOjUY;EACI;EACA;EACA;EACA;EACA,mBRxEE;EQyEF,WRtEF;EQuEE;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;APmUhB;AOjUgB;EACI;EACA;EACA;EACA;EACA;EACA;EACA,4BR9FF;ACialB;AOhUgB;EACI;APkUpB;AO7TY;EACI;EACA;EACA;AP+ThB;AO5TY;EACI,cR5GG;EQ6GH;EACA;EACA;AP8ThB;AO5TgB;EACI;AP8TpB;;AQ/aA;EACI;EACA;EACA;EACA;ARkbJ;AQhbI;EACI;ARkbR;AQ/aI;EACI;ARibR;;AQ3aA;EACI;EACA;EACA;EACA;EAEA;AR6aJ;AQ3aI;EACI;AR6aR;AQzaQ;EACI;AR2aZ;;AQraA;EACI;EACA;EACA;ARwaJ;;AQ9ZA;EACI;EACA;EACA;ARiaJ;AQ7ZI;EAEI;EACA;EACA;EACA;AR8ZR;AQ3ZQ;EACI;EACA;EACA;EACA;AR6ZZ;AQ3ZY;EACI;AR6ZhB;AQ3ZgB;EACI;AR6ZpB;AQxZY;EACI;AR0ZhB;AQxZgB;EACI;AR0ZpB;AQpZY;EACI;EACA;ARsZhB;AQjZI;EACI;EACA;EACA;EACA;ARmZR;AQhZI;EACI;EACA;EACA;ARkZR;;AShgBA;EACI;EACA;EACA;EACA;EACA;EACA;ATmgBJ;AShgBI;EACI;EACA;EACA;ATkgBR;AShgBQ;EALJ;IAMQ;ETmgBV;AACF;ASjgBQ;EACI;ATmgBZ;AShgBQ;EACI;ATkgBZ;AS3fQ;EACI;AT6fZ;AStfQ;EACI;ATwfZ;;ASjfI;EACI;EACA;EACA;EACA;ATofR;ASlfQ;EAEI;EACA;EACA;ATmfZ;ASjfY;EAEI;EACA;EACA;EACA;ATkfhB;AShfgB;EACI;ATkfpB;AShfoB;EACI;ATkfxB;ASxegB;EACI;EACA;AT0epB;ASreQ;EACI;EACA;EACA;EACA;ATueZ;ASpeQ;EACI;EACA;EACA;ATseZ,C", "sources": ["webpack://trustpilot-reviewkit/./react_app/assets/scss/previews/_common.scss", "webpack://trustpilot-reviewkit/./react_app/assets/scss/previews/_variables.scss", "webpack://trustpilot-reviewkit/./react_app/assets/scss/previews/main.scss", "webpack://trustpilot-reviewkit/./react_app/assets/scss/previews/_microreviewcount.scss", "webpack://trustpilot-reviewkit/./react_app/assets/scss/previews/_microbutton.scss", "webpack://trustpilot-reviewkit/./react_app/assets/scss/previews/_microcombo.scss", "webpack://trustpilot-reviewkit/./react_app/assets/scss/previews/_microstar.scss", "webpack://trustpilot-reviewkit/./react_app/assets/scss/previews/_microtrustscore.scss", "webpack://trustpilot-reviewkit/./react_app/assets/scss/previews/_mini.scss", "webpack://trustpilot-reviewkit/./react_app/assets/scss/previews/_starter.scss", "webpack://trustpilot-reviewkit/./react_app/assets/scss/previews/premium/_premiumgrid.scss", "webpack://trustpilot-reviewkit/./react_app/assets/scss/previews/premium/_premiumlist.scss"], "sourcesContent": ["// Review Kit common styles\n@use \"variables\";\n\n.reviewkit_fpln_common {\n    display: inline-flex;\n    gap: 5px;\n    padding: 22px 26px;\n    border-radius: 4px;\n    border: 1px solid variables.$trustpilot-color;\n    text-decoration: none;\n    font-size: 16px;\n    font-weight: 600;\n    color: variables.$body-text-color;\n\n    &.preview {\n        font-size: 12px;\n        display: flex;\n        align-items: center;\n    }\n}\n\n.reviewkit_fpln_star_icon {\n    width: 16px;\n    height: 16px;\n    margin-right: 4px;\n    fill: variables.$trustpilot-color;\n}\n\n\n/* Star Rating Container */\n.reviewkit_star_rating {\n    display: inline-flex;\n    flex-direction: row-reverse;\n    justify-content: start;\n    gap: 5px;\n    margin-bottom: 15px;\n}\n\n/* Hide radio buttons */\n.reviewkit_star_rating input {\n    display: none;\n}\n\n/* Style star labels */\n.reviewkit_star_rating label {\n    font-size: 40px;\n    line-height: 1;\n    color: #ddd;\n    cursor: pointer;\n    transition: all 0.3s ease;\n    user-select: none;\n}\n\n/* Hover effect */\n.reviewkit_star_rating label:hover {\n    transform: scale(1.1);\n}\n\n/* Default hover color (yellow) */\n.reviewkit_star_rating label:hover,\n.reviewkit_star_rating label:hover~label {\n    color: #ffce00;\n}\n\n/* 1 Star Selected - RED */\n.reviewkit_star_rating input[value=\"1\"]:checked~label[for=\"star1\"] {\n    color: #ff3722;\n}\n\n/* 2 Stars Selected - ORANGE */\n.reviewkit_star_rating input[value=\"2\"]:checked~label[for=\"star2\"],\n.reviewkit_star_rating input[value=\"2\"]:checked~label[for=\"star1\"] {\n    color: #ff8622;\n}\n\n/* 3 Stars Selected - YELLOW */\n.reviewkit_star_rating input[value=\"3\"]:checked~label[for=\"star3\"],\n.reviewkit_star_rating input[value=\"3\"]:checked~label[for=\"star2\"],\n.reviewkit_star_rating input[value=\"3\"]:checked~label[for=\"star1\"] {\n    color: #ffce00;\n}\n\n/* 4 Stars Selected - BLUE */\n.reviewkit_star_rating input[value=\"4\"]:checked~label[for=\"star4\"],\n.reviewkit_star_rating input[value=\"4\"]:checked~label[for=\"star3\"],\n.reviewkit_star_rating input[value=\"4\"]:checked~label[for=\"star2\"],\n.reviewkit_star_rating input[value=\"4\"]:checked~label[for=\"star1\"] {\n    color: #73cf11;\n}\n\n/* 5 Stars Selected - GREEN */\n.reviewkit_star_rating input[value=\"5\"]:checked~label[for=\"star5\"],\n.reviewkit_star_rating input[value=\"5\"]:checked~label[for=\"star4\"],\n.reviewkit_star_rating input[value=\"5\"]:checked~label[for=\"star3\"],\n.reviewkit_star_rating input[value=\"5\"]:checked~label[for=\"star2\"],\n.reviewkit_star_rating input[value=\"5\"]:checked~label[for=\"star1\"] {\n    color: #00b67a;\n}\n\n/* Override hover when rating is selected */\n.reviewkit_star_rating input[value=\"1\"]:checked~label:hover,\n.reviewkit_star_rating input[value=\"1\"]:checked~label:hover~label {\n    color: #ff3722;\n}\n\n.reviewkit_star_rating input[value=\"2\"]:checked~label:hover,\n.reviewkit_star_rating input[value=\"2\"]:checked~label:hover~label {\n    color: #ff8622;\n}\n\n.reviewkit_star_rating input[value=\"3\"]:checked~label:hover,\n.reviewkit_star_rating input[value=\"3\"]:checked~label:hover~label {\n    color: #ffce00;\n}\n\n.reviewkit_star_rating input[value=\"4\"]:checked~label:hover,\n.reviewkit_star_rating input[value=\"4\"]:checked~label:hover~label {\n    color: #73cf11;\n}\n\n.reviewkit_star_rating input[value=\"5\"]:checked~label:hover,\n.reviewkit_star_rating input[value=\"5\"]:checked~label:hover~label {\n    color: #00b67a;\n}", "$body-text-color: #14223b;\n$primary-color: #6c3ff2;\n$trustpilot-color: #00b67a;\n$color-white: #fff;", ".reviewkit_fpln_common {\n  display: inline-flex;\n  gap: 5px;\n  padding: 22px 26px;\n  border-radius: 4px;\n  border: 1px solid #00b67a;\n  text-decoration: none;\n  font-size: 16px;\n  font-weight: 600;\n  color: #14223b;\n}\n.reviewkit_fpln_common.preview {\n  font-size: 12px;\n  display: flex;\n  align-items: center;\n}\n\n.reviewkit_fpln_star_icon {\n  width: 16px;\n  height: 16px;\n  margin-right: 4px;\n  fill: #00b67a;\n}\n\n/* Star Rating Container */\n.reviewkit_star_rating {\n  display: inline-flex;\n  flex-direction: row-reverse;\n  justify-content: start;\n  gap: 5px;\n  margin-bottom: 15px;\n}\n\n/* Hide radio buttons */\n.reviewkit_star_rating input {\n  display: none;\n}\n\n/* Style star labels */\n.reviewkit_star_rating label {\n  font-size: 40px;\n  line-height: 1;\n  color: #ddd;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  user-select: none;\n}\n\n/* Hover effect */\n.reviewkit_star_rating label:hover {\n  transform: scale(1.1);\n}\n\n/* Default hover color (yellow) */\n.reviewkit_star_rating label:hover,\n.reviewkit_star_rating label:hover ~ label {\n  color: #ffce00;\n}\n\n/* 1 Star Selected - RED */\n.reviewkit_star_rating input[value=\"1\"]:checked ~ label[for=star1] {\n  color: #ff3722;\n}\n\n/* 2 Stars Selected - ORANGE */\n.reviewkit_star_rating input[value=\"2\"]:checked ~ label[for=star2],\n.reviewkit_star_rating input[value=\"2\"]:checked ~ label[for=star1] {\n  color: #ff8622;\n}\n\n/* 3 Stars Selected - YELLOW */\n.reviewkit_star_rating input[value=\"3\"]:checked ~ label[for=star3],\n.reviewkit_star_rating input[value=\"3\"]:checked ~ label[for=star2],\n.reviewkit_star_rating input[value=\"3\"]:checked ~ label[for=star1] {\n  color: #ffce00;\n}\n\n/* 4 Stars Selected - BLUE */\n.reviewkit_star_rating input[value=\"4\"]:checked ~ label[for=star4],\n.reviewkit_star_rating input[value=\"4\"]:checked ~ label[for=star3],\n.reviewkit_star_rating input[value=\"4\"]:checked ~ label[for=star2],\n.reviewkit_star_rating input[value=\"4\"]:checked ~ label[for=star1] {\n  color: #73cf11;\n}\n\n/* 5 Stars Selected - GREEN */\n.reviewkit_star_rating input[value=\"5\"]:checked ~ label[for=star5],\n.reviewkit_star_rating input[value=\"5\"]:checked ~ label[for=star4],\n.reviewkit_star_rating input[value=\"5\"]:checked ~ label[for=star3],\n.reviewkit_star_rating input[value=\"5\"]:checked ~ label[for=star2],\n.reviewkit_star_rating input[value=\"5\"]:checked ~ label[for=star1] {\n  color: #00b67a;\n}\n\n/* Override hover when rating is selected */\n.reviewkit_star_rating input[value=\"1\"]:checked ~ label:hover,\n.reviewkit_star_rating input[value=\"1\"]:checked ~ label:hover ~ label {\n  color: #ff3722;\n}\n\n.reviewkit_star_rating input[value=\"2\"]:checked ~ label:hover,\n.reviewkit_star_rating input[value=\"2\"]:checked ~ label:hover ~ label {\n  color: #ff8622;\n}\n\n.reviewkit_star_rating input[value=\"3\"]:checked ~ label:hover,\n.reviewkit_star_rating input[value=\"3\"]:checked ~ label:hover ~ label {\n  color: #ffce00;\n}\n\n.reviewkit_star_rating input[value=\"4\"]:checked ~ label:hover,\n.reviewkit_star_rating input[value=\"4\"]:checked ~ label:hover ~ label {\n  color: #73cf11;\n}\n\n.reviewkit_star_rating input[value=\"5\"]:checked ~ label:hover,\n.reviewkit_star_rating input[value=\"5\"]:checked ~ label:hover ~ label {\n  color: #00b67a;\n}\n\n.reviewkit_fpln_mirc .mirc_see {\n  font-weight: 500;\n}\n.reviewkit_fpln_mirc .mirc_r_count {\n  font-weight: 700;\n}\n.reviewkit_fpln_mirc .review_us_one {\n  font-weight: 500;\n}\n.reviewkit_fpln_mirc .place_name {\n  font-weight: 600;\n}\n\n.reviewkit_fpln_mcb_wrap {\n  display: flex;\n}\n.reviewkit_fpln_mcb_wrap .reviewkit_fpln_mcb_left {\n  background-color: #00b67a;\n  color: #fff;\n  display: inline-flex;\n  align-items: center;\n  gap: 6px;\n  padding: 6px 10px;\n  text-decoration: none;\n}\n.reviewkit_fpln_mcb_wrap .reviewkit_fpln_mcb_left .reviewkit_fpln_star_icon {\n  fill: #fff;\n}\n.reviewkit_fpln_mcb_wrap .reviewkit_fpln_mcb_left .place_name {\n  color: #fff;\n  font-size: 16px;\n  font-weight: 500;\n  line-height: 1;\n}\n.reviewkit_fpln_mcb_wrap .reviewkit_fpln_mcb_right {\n  display: flex;\n  border: 1px solid #cfd0cf;\n  align-items: center;\n  background-color: #fff;\n  font-size: 16px;\n  line-height: 1;\n  color: #555;\n  padding: 6px 10px;\n}\n\n.reviewkit_fpln_mc {\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 15px;\n}\n.reviewkit_fpln_mc .reviewkit_fpln_mc_inner_left {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 15px;\n}\n.reviewkit_fpln_mc .reviewkit_fpln_mc_inner_left .review_us_one {\n  font-weight: 700;\n}\n.reviewkit_fpln_mc .reviewkit_fpln_mc_inner_left .reviewkit_star_rating {\n  display: inline-flex;\n  margin-bottom: 0;\n  width: 150px;\n}\n.reviewkit_fpln_mc .reviewkit_fpln_mc_inner_left .reviewkit_star_rating img {\n  width: 100%;\n}\n.reviewkit_fpln_mc .reviewkit_fpln_mc_inner_left .reviewkit_star_rating label {\n  font-size: 16px;\n  padding: 4px;\n  color: #fff;\n  background: #00b67a;\n}\n.reviewkit_fpln_mc .reviewkit_fpln_mc_inner_right {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 6px;\n}\n.reviewkit_fpln_mc .reviewkit_fpln_mc_inner_right .review_us_one {\n  font-weight: 400;\n}\n.reviewkit_fpln_mc .reviewkit_fpln_mc_inner_right .reviewkit_fpln_star_icon {\n  margin-right: 0;\n}\n\n.reviewkit_fpln_mcs.frontend .dynamic_rating, .reviewkit_fpln_mcs.frontend .place_name {\n  font-weight: 600;\n  font-size: 18px;\n}\n.reviewkit_fpln_mcs.frontend .dynamic_stars {\n  width: 150px;\n}\n.reviewkit_fpln_mcs .dynamic_rating, .reviewkit_fpln_mcs .place_name {\n  font-weight: 600;\n  font-size: 14px;\n}\n.reviewkit_fpln_mcs .dynamic_stars {\n  width: 100px;\n  line-height: 0;\n}\n.reviewkit_fpln_mcs > .tp-wrapper {\n  display: flex;\n  align-items: center;\n  column-gap: 5px;\n}\n.reviewkit_fpln_mcs > .tp-wrapper img {\n  width: 20px;\n}\n\n.reviewkit_fpln_mcs {\n  align-items: center;\n  gap: 15px;\n}\n.reviewkit_fpln_mcs .reviewkit_fpln_mc_inner_left {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 15px;\n}\n.reviewkit_fpln_mcs .reviewkit_fpln_mc_inner_left .review_us_one {\n  font-weight: 700;\n}\n.reviewkit_fpln_mcs .reviewkit_fpln_mc_inner_left .reviewkit_star_rating {\n  display: inline-flex;\n  margin-bottom: 0;\n}\n.reviewkit_fpln_mcs .reviewkit_fpln_mc_inner_left .reviewkit_star_rating label {\n  font-size: 26px;\n  color: #00b67a;\n}\n.reviewkit_fpln_mcs .reviewkit_fpln_mc_inner_right .review_us_one {\n  font-weight: 400;\n}\n.reviewkit_fpln_mcs .reviewkit_fpln_mc_inner_right .reviewkit_fpln_star_icon {\n  margin-right: 0;\n}\n\n.reviewkit_fpln_mcts {\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 10px;\n}\n.reviewkit_fpln_mcts .reviewkit_fpln_inner_left {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 10px;\n}\n.reviewkit_fpln_mcts .reviewkit_fpln_inner_left .reviewkit_out_of {\n  font-weight: 400;\n  word-spacing: 1px;\n}\n.reviewkit_fpln_mcts .reviewkit_fpln_inner_right {\n  display: flex;\n  align-items: center;\n  column-gap: 5px;\n}\n.reviewkit_fpln_mcts .reviewkit_fpln_inner_right .reviewkit_fpln_star_icon {\n  margin-right: 0;\n}\n\n.reviewkit_fpln_mcts.preview .reviewkit_fpln_inner_right {\n  display: flex;\n  align-items: center;\n  column-gap: 3px;\n}\n.reviewkit_fpln_mcts.preview .reviewkit_fpln_inner_right img {\n  width: 15px;\n}\n\n.reviewkit_fpln_mini.preview .reviewkit_fpln_inner_top .place_name {\n  font-size: 24px;\n}\n\n.reviewkit_fpln_mini {\n  width: 300px;\n  display: inline-flex;\n  flex-direction: column;\n}\n.reviewkit_fpln_mini .reviewkit_fpln_inner_top {\n  margin-bottom: 8px;\n  display: flex;\n  align-items: center;\n  column-gap: 3px;\n}\n.reviewkit_fpln_mini .reviewkit_fpln_inner_top .reviewkit_fpln_star_icon {\n  margin-right: 0;\n}\n.reviewkit_fpln_mini .reviewkit_fpln_inner_top > span {\n  font-weight: bold;\n}\n.reviewkit_fpln_mini .reviewkit_fpln_inner_top img {\n  width: 30px;\n}\n.reviewkit_fpln_mini .reviewkit_bg img {\n  width: 100%;\n  margin-bottom: 4px;\n}\n.reviewkit_fpln_mini .reviewkit_fpln_inner_bottom {\n  display: flex;\n  gap: 20px;\n  font-size: 1.2rem;\n}\n.reviewkit_fpln_mini .reviewkit_fpln_inner_bottom .reviewkit_left_reviews {\n  position: relative;\n}\n.reviewkit_fpln_mini .reviewkit_fpln_inner_bottom .reviewkit_left_reviews::before {\n  position: absolute;\n  content: \"\";\n  top: 50%;\n  right: -10px;\n  height: 14px;\n  transform: translateY(-50%);\n  width: 1px;\n  background: #14223b;\n}\n.reviewkit_fpln_mini .reviewkit_fpln_inner_bottom .reviewkit_left_reviews .reviewkit_orignal_rcount {\n  font-weight: 600;\n}\n.reviewkit_fpln_mini .reviewkit_fpln_inner_bottom .reviewkit_review_area .reviewkit_out_of {\n  font-weight: 600;\n}\n.reviewkit_fpln_starter.preview .reviewkit_toltip_wrap .reviewkit_logo_container img {\n  width: 20px;\n}\n\n.reviewkit_fpln_starter {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n.reviewkit_fpln_starter .reviewkit_fpln_inner_top {\n  margin-bottom: 6px;\n}\n.reviewkit_fpln_starter .reviewkit_fpln_inner_top .mirc_r_count {\n  font-weight: 600;\n}\n.reviewkit_fpln_starter .reviewkit_bg img {\n  width: 150px;\n  margin-bottom: 4px;\n}\n.reviewkit_fpln_starter .reviewkit_toltip_wrap {\n  display: flex;\n  align-items: center;\n}\n.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container {\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n  gap: 8px;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container .reviewkit_star {\n  width: 20px;\n  height: 20px;\n  background-color: #00b67a;\n  clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);\n  transition: transform 0.2s ease;\n}\n.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container .reviewkit_text {\n  font-size: 18px;\n  font-weight: 600;\n  color: #14223b;\n  letter-spacing: -0.5px;\n}\n.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container:hover .reviewkit_star {\n  transform: scale(1.1);\n}\n.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container .reviewkit_tooltip {\n  position: absolute;\n  top: 100%;\n  left: 50%;\n  transform: translateX(-50%);\n  background: #14223b;\n  color: #fff;\n  padding: 16px 20px;\n  border-radius: 8px;\n  font-size: 14px;\n  white-space: nowrap;\n  opacity: 0;\n  visibility: hidden;\n  -webkit-transition: all 0.3s ease;\n  transition: all 0.3s ease;\n  z-index: 10;\n  margin-bottom: 0;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n  text-align: center;\n}\n.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container .reviewkit_tooltip::after {\n  content: \"\";\n  position: absolute;\n  top: -16px;\n  left: 50%;\n  transform: translateX(-50%);\n  border: 8px solid transparent;\n  border-bottom-color: #14223b;\n}\n.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container .reviewkit_tooltip .reviewkit_tooltip_content {\n  margin-bottom: 6px;\n}\n.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container:hover .reviewkit_tooltip {\n  opacity: 1;\n  visibility: visible;\n  transform: translateX(-50%) translateY(7px);\n}\n.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container .reviewkit_tooltip_link {\n  color: #00b67a;\n  text-decoration: none;\n  font-weight: 500;\n  transition: color 0.3s ease;\n}\n.reviewkit_fpln_starter .reviewkit_toltip_wrap .reviewkit_logo_container .reviewkit_tooltip_link:hover {\n  text-decoration: underline;\n}\n\n.reviewkit_header {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 15px;\n}\n.reviewkit_header img {\n  width: 150px;\n}\n.reviewkit_header .reviewkit_profile_name {\n  margin: 0;\n}\n\n.reviewkit_profile_review {\n  position: relative;\n  display: flex;\n  gap: 5px;\n  align-items: center;\n  margin-bottom: 15px;\n}\n.reviewkit_profile_review .reviewkit_review_secore {\n  font-weight: 700;\n}\n.reviewkit_profile_review .reviewkit_review_img img {\n  width: 150px;\n}\n\n.reviewkit_prium_grid_layout {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 20px;\n}\n\n.reviewkit_prium_grid__single_item {\n  padding: 15px;\n  border-radius: 6px;\n  background: #fafafa;\n}\n.reviewkit_prium_grid__single_item .reviewkit_ratting_author_time_wrap {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  flex-wrap: wrap;\n}\n.reviewkit_prium_grid__single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_author_left {\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n  margin-bottom: 15px;\n}\n.reviewkit_prium_grid__single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_author_left .reviewkit_star_rating {\n  margin-bottom: 0;\n}\n.reviewkit_prium_grid__single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_author_left .reviewkit_star_rating label {\n  font-size: 30px;\n}\n.reviewkit_prium_grid__single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_author_left .reviewkit_ratting_author {\n  color: #98999a;\n}\n.reviewkit_prium_grid__single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_author_left .reviewkit_ratting_author span {\n  color: #484849;\n}\n.reviewkit_prium_grid__single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_time_right P {\n  margin: 0;\n  color: #98999a;\n}\n.reviewkit_prium_grid__single_item .reviewkit_title {\n  font-size: 20px;\n  font-weight: 600;\n  line-height: 1;\n  margin: 0 0 15px;\n}\n.reviewkit_prium_grid__single_item .reviewkit_pra {\n  font-size: 16px;\n  line-height: 24px;\n  margin: 0;\n}\n\n.reviewkit_prium_header {\n  display: flex;\n  justify-content: center;\n  flex-direction: column;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 30px;\n}\n.reviewkit_prium_header .reviewkit_prim_top {\n  display: flex;\n  gap: 20px;\n  flex-wrap: wrap;\n}\n@media screen and (max-width: 450px) {\n  .reviewkit_prium_header .reviewkit_prim_top {\n    gap: 10px;\n  }\n}\n.reviewkit_prium_header .reviewkit_prim_top .reviewkit_profile_name {\n  margin: 0;\n}\n.reviewkit_prium_header .reviewkit_prim_top .reviewkit_rating_bg {\n  width: 150px;\n}\n.reviewkit_prium_header .reviewkit_prim_bottom .reviewkit_prim_r_count {\n  font-weight: 600;\n}\n.reviewkit_prium_header .reviewkit_prim_bottom .place_name {\n  font-weight: 600;\n}\n\n.reviewkit_prium_list_layout .reviewkit_prium_list_single_item {\n  padding: 15px;\n  border-radius: 6px;\n  background-color: #fafafa;\n  margin-bottom: 30px;\n}\n.reviewkit_prium_list_layout .reviewkit_prium_list_single_item .reviewkit_ratting_author_time_wrap {\n  display: flex;\n  justify-content: space-between;\n  flex-wrap: wrap;\n}\n.reviewkit_prium_list_layout .reviewkit_prium_list_single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_author_left {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 10px;\n}\n.reviewkit_prium_list_layout .reviewkit_prium_list_single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_author_left .reviewkit_star_rating {\n  margin-bottom: 0;\n}\n.reviewkit_prium_list_layout .reviewkit_prium_list_single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_author_left .reviewkit_star_rating label {\n  font-size: 30px;\n}\n.reviewkit_prium_list_layout .reviewkit_prium_list_single_item .reviewkit_ratting_author_time_wrap .reviewkit_ratting_time_right P {\n  margin: 0;\n  color: #98999a;\n}\n.reviewkit_prium_list_layout .reviewkit_prium_list_single_item .reviewkit_title {\n  font-size: 20px;\n  font-weight: 600;\n  line-height: 1;\n  margin: 0 0 15px;\n}\n.reviewkit_prium_list_layout .reviewkit_prium_list_single_item .reviewkit_pra {\n  font-size: 16px;\n  line-height: 24px;\n  margin: 0;\n}", "// Micro Reivew Count\n@use \"variables\";\n\n.reviewkit_fpln_mirc {\n    .mirc_see {\n        font-weight: 500;\n    }\n\n    .mirc_r_count {\n        font-weight: 700;\n    }\n\n    .review_us_one {\n        font-weight: 500;\n    }\n\n    .eviewkit_fpln_star_icon {}\n\n    .place_name {\n        font-weight: 600;\n    }\n}", "// micro button reviews style\n.reviewkit_fpln_mcb_wrap {\n\n    display: flex;\n\n    .reviewkit_fpln_mcb_left {\n        background-color: #00b67a;\n        color: #fff;\n        display: inline-flex;\n        align-items: center;\n        gap: 6px;\n        padding: 6px 10px;\n        text-decoration: none;\n\n        .reviewkit_fpln_star_icon {\n            fill: #fff;\n        }\n\n        .place_name {\n            color: #fff;\n            font-size: 16px;\n            font-weight: 500;\n            line-height: 1;\n        }\n    }\n\n    .reviewkit_fpln_mcb_right {\n\n\n        display: flex;\n        border: 1px solid #cfd0cf;\n        align-items: center;\n        background-color: #fff;\n        font-size: 16px;\n        line-height: 1;\n        color: #555;\n        padding: 6px 10px;\n\n    }\n}", "@use \"variables\";\n\n.reviewkit_fpln_mc {\n    align-items: center;\n    flex-wrap: wrap;\n    gap: 15px;\n\n    .reviewkit_fpln_mc_inner_left {\n        display: flex;\n        align-items: center;\n        flex-wrap: wrap;\n        gap: 15px;\n\n        .review_us_one {\n            font-weight: 700;\n        }\n\n        .reviewkit_star_rating {\n            display: inline-flex;\n            margin-bottom: 0;\n            width: 150px;\n\n            img {\n                width: 100%;\n            }\n\n            label {\n                font-size: 16px;\n                padding: 4px;\n                color: variables.$color-white;\n                background: variables.$trustpilot-color;\n            }\n        }\n    }\n\n    .reviewkit_fpln_mc_inner_right {\n        display: flex;\n        flex-wrap: wrap;\n        gap: 6px;\n\n        .review_us_one {\n            font-weight: 400;\n        }\n\n        .reviewkit_fpln_star_icon {\n            margin-right: 0;\n        }\n    }\n\n}", "@use \"variables\";\n\n.reviewkit_fpln_mcs {\n\n    &.frontend{\n        .dynamic_rating, .place_name{\n            font-weight: 600;\n            font-size: 18px;\n        } \n        .dynamic_stars {\n            width: 150px;\n        }\n    }\n\n    .dynamic_rating, .place_name{\n        font-weight: 600;\n        font-size: 14px;\n    }\n\n    .dynamic_stars {\n        width: 100px;\n        line-height: 0;\n    }\n\n    > .tp-wrapper{\n        display: flex;\n        align-items: center;\n        column-gap: 5px;\n         img {\n            width: 20px;\n\n        }\n    }\n}\n\n.reviewkit_fpln_mcs {\n    align-items: center;\n    // flex-wrap: wrap;\n    gap: 15px;\n\n    .reviewkit_fpln_mc_inner_left {\n        display: flex;\n        align-items: center;\n        flex-wrap: wrap;\n        gap: 15px;\n\n        .review_us_one {\n            font-weight: 700;\n        }\n\n        .reviewkit_star_rating {\n            display: inline-flex;\n            margin-bottom: 0;\n\n            label {\n                font-size: 26px;\n                color: variables.$trustpilot-color;\n            }\n        }\n    }\n\n    .reviewkit_fpln_mc_inner_right {\n        .review_us_one {\n            font-weight: 400;\n        }\n\n        .reviewkit_fpln_star_icon {\n            margin-right: 0;\n        }\n    }\n\n}", ".reviewkit_fpln_mcts {\n    align-items: center;\n    flex-wrap: wrap;\n    gap: 10px;\n\n    .reviewkit_fpln_inner_left {\n        display: flex;\n        flex-wrap: wrap;\n        gap: 10px;\n\n        .reviewkit_out_of {\n            font-weight: 400;\n            word-spacing: 1px;\n        }\n    }\n\n    .reviewkit_fpln_inner_right {\n        display: flex;\n        align-items: center;\n        column-gap: 5px;\n        .reviewkit_fpln_star_icon {\n            margin-right: 0;\n        }\n    }\n}\n\n.reviewkit_fpln_mcts.preview {\n    .reviewkit_fpln_inner_right {\n        display: flex;\n        align-items:center;\n        column-gap: 3px;\n        img {\n            width: 15px;\n        }\n    }\n}", "@use \"variables\";\n\n\n.reviewkit_fpln_mini {\n    &.preview {\n        .reviewkit_fpln_inner_top{\n            .place_name {\n                font-size: 24px;\n            }\n        }\n    }\n}\n\n.reviewkit_fpln_mini {\n    width: 300px;\n    display: inline-flex;\n    // flex-wrap: wrap;\n    flex-direction: column;\n\n    .reviewkit_fpln_inner_top {\n        margin-bottom: 8px;\n        display: flex;\n        align-items: center;\n        column-gap: 3px;\n\n        .reviewkit_fpln_star_icon {\n            margin-right: 0;\n        }\n\n        > span {\n            font-weight: bold;\n        }\n\n        img {\n            width: 30px;\n        }\n\n    }\n\n    .reviewkit_bg {\n        img {\n            width: 100%;\n            margin-bottom: 4px;\n        }\n    }\n\n    .reviewkit_fpln_inner_bottom {\n        display: flex;\n        gap: 20px;\n        font-size: 1.2rem;\n\n        .reviewkit_left_reviews {\n            position: relative;\n\n            &::before {\n                position: absolute;\n                content: \"\";\n                top: 50%;\n                right: -10px;\n                height: 14px;\n                transform: translateY(-50%);\n                width: 1px;\n                background: variables.$body-text-color;\n            }\n\n            .review_us_one {}\n\n            .reviewkit_orignal_rcount {\n                font-weight: 600;\n            }\n        }\n\n        .reviewkit_review_area {\n            .reviewkit_out_of {\n                font-weight: 600;\n            }\n\n            .reviewkit_reviews {}\n\n        }\n    }\n\n}", "@use \"variables\";\n\n.reviewkit_fpln_starter.preview {\n    .reviewkit_toltip_wrap {\n        .reviewkit_logo_container {\n            img {\n                width: 20px;\n            }\n        }\n    }\n}\n\n.reviewkit_fpln_starter {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n\n    .reviewkit_fpln_inner_top {\n        margin-bottom: 6px;\n\n        .mirc_see {}\n\n        .mirc_r_count {\n            font-weight: 600;\n        }\n\n        .review_us_one {}\n    }\n\n    .reviewkit_bg {\n        img {\n            width: 150px;\n            margin-bottom: 4px;\n        }\n    }\n\n    .reviewkit_toltip_wrap {\n        display: flex;\n        align-items: center;\n\n        .reviewkit_logo_container {\n            position: relative;\n            display: inline-flex;\n            align-items: center;\n            gap: 8px;\n            border-radius: 6px;\n            cursor: pointer;\n            transition: all 0.3s ease;\n            .reviewkit_star {\n                width: 20px;\n                height: 20px;\n                background-color: variables.$trustpilot-color;\n                clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);\n                transition: transform 0.2s ease;\n            }\n\n            .reviewkit_text {\n                font-size: 18px;\n                font-weight: 600;\n                color: variables.$body-text-color;\n                letter-spacing: -0.5px;\n            }\n\n            &:hover .reviewkit_star {\n                transform: scale(1.1);\n            }\n\n            .reviewkit_tooltip {\n                position: absolute;\n                top: 100%;\n                left: 50%;\n                transform: translateX(-50%);\n                background: variables.$body-text-color;\n                color: variables.$color-white;\n                padding: 16px 20px;\n                border-radius: 8px;\n                font-size: 14px;\n                white-space: nowrap;\n                opacity: 0;\n                visibility: hidden;\n                -webkit-transition: all 0.3s ease;\n                transition: all 0.3s ease;\n                z-index: 10;\n                margin-bottom: 0;\n                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n                text-align: center;\n\n                &::after {\n                    content: \"\";\n                    position: absolute;\n                    top: -16px;\n                    left: 50%;\n                    transform: translateX(-50%);\n                    border: 8px solid transparent;\n                    border-bottom-color: variables.$body-text-color;\n                }\n\n                .reviewkit_tooltip_content {\n                    margin-bottom: 6px;\n                }\n\n            }\n\n            &:hover .reviewkit_tooltip {\n                opacity: 1;\n                visibility: visible;\n                transform: translateX(-50%) translateY(7px);\n            }\n\n            .reviewkit_tooltip_link {\n                color: variables.$trustpilot-color;\n                text-decoration: none;\n                font-weight: 500;\n                transition: color 0.3s ease;\n\n                &:hover {\n                    text-decoration: underline;\n                }\n            }\n\n        }\n\n    }\n}", "// review header\n@use \"../variables\";\n\n.reviewkit_header {\n    display: flex;\n    align-items: center;\n    gap: 10px;\n    margin-bottom: 15px;\n\n    img {\n        width: 150px;\n    }\n\n    .reviewkit_profile_name {\n        margin: 0;\n    }\n\n}\n\n// review profile\n.reviewkit_profile_review {\n    position: relative;\n    display: flex;\n    gap: 5px;\n    align-items: center;\n\n    margin-bottom: 15px;\n\n    .reviewkit_review_secore {\n        font-weight: 700;\n    }\n\n    .reviewkit_review_img {\n        img {\n            width: 150px;\n        }\n    }\n\n}\n\n.reviewkit_prium_grid_layout {\n    display: grid;\n    grid-template-columns: repeat(3, 1fr);\n    gap: 20px;\n\n}\n\n// display: flex;\n// align-items: center;\n// justify-content: space-between;\n// flex-wrap: wrap;\n\n// single item\n.reviewkit_prium_grid__single_item {\n    padding: 15px;\n    border-radius: 6px;\n    background: #fafafa;\n\n    // border: 1px solid $primary-color;\n    // margin-bottom: 15px;\n    .reviewkit_ratting_author_time_wrap {\n\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        flex-wrap: wrap;\n\n\n        .reviewkit_ratting_author_left {\n            display: flex;\n            flex-direction: column;\n            gap: 6px;\n            margin-bottom: 15px;\n\n            .reviewkit_star_rating {\n                margin-bottom: 0;\n\n                label {\n                    font-size: 30px;\n                }\n\n            }\n\n            .reviewkit_ratting_author {\n                color: #98999a;\n\n                span {\n                    color: #484849;\n                }\n            }\n        }\n\n        .reviewkit_ratting_time_right {\n            P {\n                margin: 0;\n                color: #98999a;\n            }\n        }\n    }\n\n    .reviewkit_title {\n        font-size: 20px;\n        font-weight: 600;\n        line-height: 1;\n        margin: 0 0 15px;\n    }\n\n    .reviewkit_pra {\n        font-size: 16px;\n        line-height: 24px;\n        margin: 0;\n    }\n\n}", ".reviewkit_prium_header {\n    display: flex;\n    justify-content: center;\n    flex-direction: column;\n    align-items: center;\n    gap: 10px;\n    margin-bottom: 30px;\n\n\n    .reviewkit_prim_top {\n        display: flex;\n        gap: 20px;\n        flex-wrap: wrap;\n\n        @media screen and (max-width: 450px) {\n            gap: 10px;\n        }\n\n        .reviewkit_profile_name {\n            margin: 0;\n        }\n\n        .reviewkit_rating_bg {\n            width: 150px;\n        }\n    }\n\n    .reviewkit_prim_bottom {\n        .reviewkit_prim_see {}\n\n        .reviewkit_prim_r_count {\n            font-weight: 600;\n        }\n\n        .review_us_one {}\n\n        .reviewkit_fpln_star_icon {}\n\n        .place_name {\n            font-weight: 600;\n        }\n    }\n}\n\n// singel item\n.reviewkit_prium_list_layout {\n    .reviewkit_prium_list_single_item {\n        padding: 15px;\n        border-radius: 6px;\n        background-color: #fafafa;\n        margin-bottom: 30px;\n\n        .reviewkit_ratting_author_time_wrap {\n\n            display: flex;\n            justify-content: space-between;\n            flex-wrap: wrap;\n\n            .reviewkit_ratting_author_left {\n\n                display: flex;\n                align-items: center;\n                flex-wrap: wrap;\n                gap: 10px;\n\n                .reviewkit_star_rating {\n                    margin-bottom: 0;\n\n                    label {\n                        font-size: 30px;\n                    }\n                }\n\n                .reviewkit_ratting_author {\n                    span {}\n                }\n            }\n\n            .reviewkit_ratting_time_right {\n                P {\n                    margin: 0;\n                    color: #98999a;\n                }\n            }\n        }\n\n        .reviewkit_title {\n            font-size: 20px;\n            font-weight: 600;\n            line-height: 1;\n            margin: 0 0 15px;\n        }\n\n        .reviewkit_pra {\n            font-size: 16px;\n            line-height: 24px;\n            margin: 0;\n        }\n    }\n\n}"], "names": [], "sourceRoot": ""}