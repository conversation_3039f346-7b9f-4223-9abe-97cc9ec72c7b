/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/css-loader@6.11.0_webpack@5.101.0/node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!./node_modules/.pnpm/postcss-loader@6.2.1_postcss@8.5.6_webpack@5.101.0/node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./node_modules/.pnpm/sass-loader@16.0.5_sass@1.89.2_webpack@5.101.0/node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[3]!./react_app/assets/scss/main.scss ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
/* @use "../../../node_modules/@picocss/pico/scss/pico.scss"; */
.trustpilot-star-rating, .star-rating {
  display: flex;
  align-items: center;
  gap: 8px;
}
.trustpilot-star-rating .stars, .star-rating .stars {
  display: flex;
  align-items: center;
  gap: 3px;
}
.trustpilot-star-rating .star, .star-rating .star {
  display: inline-block;
  line-height: 1;
  transition: all 0.3s ease;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.trustpilot-star-rating .star.star-half, .star-rating .star.star-half {
  position: relative;
}
.trustpilot-star-rating .star.star-half::after, .star-rating .star.star-half::after {
  content: "★";
  position: absolute;
  left: 50%;
  top: 0;
  color: #ddd;
  z-index: -1;
}
.trustpilot-star-rating .star.star-empty, .star-rating .star.star-empty {
  color: #ddd;
}
.trustpilot-star-rating .star:hover, .star-rating .star:hover {
  transform: scale(1.05);
}
.trustpilot-star-rating .star-svg, .star-rating .star-svg {
  display: inline-block;
  transition: all 0.3s ease;
}
.trustpilot-star-rating .star-svg.star-empty, .star-rating .star-svg.star-empty {
  fill: #ddd;
}
.trustpilot-star-rating .star-svg:hover, .star-rating .star-svg:hover {
  transform: scale(1.05);
}
.trustpilot-star-rating .star-svg-half, .star-rating .star-svg-half {
  display: inline-block;
  position: relative;
}
.trustpilot-star-rating .rating-value, .star-rating .rating-value {
  font-size: 0.9em;
  color: #666;
  font-weight: 500;
  margin-left: 4px;
}
.trustpilot-star-rating.trustpilot-star-rating--small, .trustpilot-star-rating--small.star-rating {
  gap: 6px;
}
.trustpilot-star-rating.trustpilot-star-rating--small .stars, .trustpilot-star-rating--small.star-rating .stars {
  gap: 2px;
}
.trustpilot-star-rating.trustpilot-star-rating--small .star, .trustpilot-star-rating--small.star-rating .star {
  font-size: 14px;
}
.trustpilot-star-rating.trustpilot-star-rating--small .star-svg, .trustpilot-star-rating--small.star-rating .star-svg {
  width: 14px;
  height: 14px;
}
.trustpilot-star-rating.trustpilot-star-rating--small .rating-value, .trustpilot-star-rating--small.star-rating .rating-value {
  font-size: 12px;
}
.trustpilot-star-rating.trustpilot-star-rating--medium, .trustpilot-star-rating--medium.star-rating {
  gap: 8px;
}
.trustpilot-star-rating.trustpilot-star-rating--medium .stars, .trustpilot-star-rating--medium.star-rating .stars {
  gap: 3px;
}
.trustpilot-star-rating.trustpilot-star-rating--medium .star, .trustpilot-star-rating--medium.star-rating .star {
  font-size: 18px;
}
.trustpilot-star-rating.trustpilot-star-rating--medium .star-svg, .trustpilot-star-rating--medium.star-rating .star-svg {
  width: 18px;
  height: 18px;
}
.trustpilot-star-rating.trustpilot-star-rating--medium .rating-value, .trustpilot-star-rating--medium.star-rating .rating-value {
  font-size: 14px;
}
.trustpilot-star-rating.trustpilot-star-rating--large, .trustpilot-star-rating--large.star-rating {
  gap: 10px;
}
.trustpilot-star-rating.trustpilot-star-rating--large .stars, .trustpilot-star-rating--large.star-rating .stars {
  gap: 4px;
}
.trustpilot-star-rating.trustpilot-star-rating--large .star, .trustpilot-star-rating--large.star-rating .star {
  font-size: 26px;
}
.trustpilot-star-rating.trustpilot-star-rating--large .star-svg, .trustpilot-star-rating--large.star-rating .star-svg {
  width: 26px;
  height: 26px;
}
.trustpilot-star-rating.trustpilot-star-rating--large .rating-value, .trustpilot-star-rating--large.star-rating .rating-value {
  font-size: 16px;
  font-weight: 600;
}

.customer-info {
  display: flex;
  align-items: center;
  gap: 12px;
}
.customer-info .customer-avatar {
  position: relative;
  border-radius: 50%;
  overflow: hidden;
  background-color: #f5f5f5;
  flex-shrink: 0;
  box-shadow: rgba(0, 0, 0, 0.02) 0px 1px 3px 0px, rgba(27, 31, 35, 0.15) 0px 0px 0px 1px;
}
.customer-info .customer-avatar .customer-image {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 50%;
}
.customer-info .customer-avatar .customer-initials {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  font-weight: 600;
  border-radius: 50%;
}
.customer-info .customer-details {
  flex: 1;
  min-width: 0;
}
.customer-info .customer-details .customer-name {
  font-weight: 500;
  color: #333;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.customer-info.customer-info--small {
  gap: 8px;
}
.customer-info.customer-info--small .customer-avatar {
  width: 32px;
  height: 32px;
}
.customer-info.customer-info--small .customer-avatar .customer-initials {
  font-size: 12px;
}
.customer-info.customer-info--small .customer-details .customer-name {
  font-size: 14px;
}
.customer-info.customer-info--medium {
  gap: 12px;
}
.customer-info.customer-info--medium .customer-avatar {
  width: 48px;
  height: 48px;
}
.customer-info.customer-info--medium .customer-avatar .customer-initials {
  font-size: 16px;
}
.customer-info.customer-info--medium .customer-details .customer-name {
  font-size: 16px;
}
.customer-info.customer-info--large {
  gap: 16px;
}
.customer-info.customer-info--large .customer-avatar {
  width: 64px;
  height: 64px;
}
.customer-info.customer-info--large .customer-avatar .customer-initials {
  font-size: 20px;
}
.customer-info.customer-info--large .customer-details .customer-name {
  font-size: 18px;
  font-weight: 600;
}

.review-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  transition: box-shadow 0.2s ease, transform 0.2s ease;
}
.review-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  cursor: pointer;
}
.review-card .review-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}
.review-card .review-header .review-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}
.review-card .review-header .review-meta .review-date {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}
.review-card .review-content .review-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.4;
}
.review-card .review-content .review-body {
  font-size: 14px;
  line-height: 1.6;
  color: #555;
  margin: 0 0 12px 0;
  word-wrap: break-word;
}
.review-card .review-content .review-experience-date {
  margin-top: 8px;
}
.review-card .review-content .review-experience-date small {
  color: #888;
  font-size: 12px;
}
.review-card .review-footer {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}
.review-card .review-footer .review-link {
  color: #007bff;
  text-decoration: none;
  font-size: 13px;
  font-weight: 500;
  transition: color 0.2s ease;
}
.review-card .review-footer .review-link:hover {
  color: #0056b3;
  text-decoration: underline;
}
.review-card .review-footer .review-link::after {
  content: " ↗";
  font-size: 11px;
}
@media (max-width: 768px) {
  .review-card {
    padding: 16px;
    margin-bottom: 12px;
  }
  .review-card .review-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  .review-card .review-header .review-meta {
    align-items: flex-start;
    flex-direction: row;
    gap: 12px;
  }
  .review-card .review-content .review-title {
    font-size: 15px;
  }
  .review-card .review-content .review-body {
    font-size: 13px;
  }
}

.trustpilot-business-page {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  margin-top: 20px;
}
.trustpilot-business-page .business-placeholder {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}
.trustpilot-business-page .business-placeholder p {
  margin: 0;
  font-style: italic;
  font-size: 22px;
}
.trustpilot-business-page .business-header {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}
.trustpilot-business-page .business-header .business-logo {
  position: relative;
  width: 122px;
  flex-shrink: 0;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f8f9fa;
  padding: 8px;
  box-shadow: rgba(0, 0, 0, 0.02) 0px 1px 3px 0px, rgba(27, 31, 35, 0.15) 0px 0px 0px 1px;
}
.trustpilot-business-page .business-header .business-logo .business-image {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
}
.trustpilot-business-page .business-header .business-logo .business-initials {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff6d00, #ff8f00);
  color: white;
  font-size: 28px;
  font-weight: 700;
}
.trustpilot-business-page .business-header .business-info {
  flex: 1;
  min-width: 0;
}
.trustpilot-business-page .business-header .business-info .business-name-section {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}
.trustpilot-business-page .business-header .business-info .business-name-section .business-name {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin: 0;
  line-height: 1.2;
}
.trustpilot-business-page .business-header .business-info .business-name-section .business-claimed,
.trustpilot-business-page .business-header .business-info .business-name-section .business-verified {
  background: #28a745;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  white-space: nowrap;
}
.trustpilot-business-page .business-header .business-info .business-name-section .business-verified {
  background: #007bff;
}
.trustpilot-business-page .business-header .business-info .business-website {
  color: #666;
  margin: 0 0 4px 0;
  font-size: 14px;
}
.trustpilot-business-page .business-header .business-info .business-category {
  color: #888;
  margin: 0 0 16px 0;
  font-size: 13px;
  font-style: italic;
}
.trustpilot-business-page .business-header .business-info .business-rating {
  margin-bottom: 16px;
}
.trustpilot-business-page .business-header .business-info .business-rating .rating-percentage {
  display: flex;
  align-items: center;
  -moz-column-gap: 10px;
       column-gap: 10px;
  margin-bottom: 10px;
}
.trustpilot-business-page .business-header .business-info .business-rating .rating-percentage .dynamic_stars {
  width: 200px;
  line-height: 0;
}
.trustpilot-business-page .business-header .business-info .business-rating .rating-percentage .dynamic_stars img {
  width: 100%;
}
.trustpilot-business-page .business-header .business-info .business-rating .rating-percentage > span {
  font-size: 22px;
  font-weight: 600;
}
.trustpilot-business-page .business-header .business-info .business-rating .review-count {
  margin-left: 8px;
  color: #666;
  font-size: 14px;
}
.trustpilot-business-page .business-header .business-info .business-links .business-website-link {
  background: #007bff;
  color: white;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}
.trustpilot-business-page .business-header .business-info .business-links .business-website-link:hover {
  background: #0056b3;
  color: white;
}
.trustpilot-business-page .business-header .business-info .business-links .business-website-link::after {
  content: " ↗";
  margin-left: 4px;
  font-size: 12px;
}
.trustpilot-business-page .business-stats {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}
.trustpilot-business-page .business-stats .stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.trustpilot-business-page .business-stats .stat-item .stat-label {
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
.trustpilot-business-page .business-stats .stat-item .stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
@media (max-width: 768px) {
  .trustpilot-business-page {
    padding: 20px;
  }
  .trustpilot-business-page .business-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  .trustpilot-business-page .business-header .business-logo {
    align-self: center;
  }
  .trustpilot-business-page .business-header .business-info .business-name-section {
    justify-content: center;
  }
  .trustpilot-business-page .business-header .business-info .business-name-section .business-name {
    font-size: 20px;
  }
  .trustpilot-business-page .business-stats {
    justify-content: center;
    gap: 16px;
  }
  .trustpilot-business-page .business-stats .stat-item {
    text-align: center;
  }
}

.review-list {
  margin-top: 20px;
}
.review-list .review-list-header {
  margin-bottom: 24px;
}
.review-list .review-list-header .review-list-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}
.review-list .review-list-header .review-filters .rating-filter {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}
.review-list .review-list-header .review-filters .rating-filter .filter-btn {
  padding: 6px 12px;
  border: 1px solid #ddd;
  background: white;
  color: #666;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}
.review-list .review-list-header .review-filters .rating-filter .filter-btn:hover {
  border-color: #007bff;
  color: #007bff;
}
.review-list .review-list-header .review-filters .rating-filter .filter-btn.active {
  background: #007bff;
  border-color: #007bff;
  color: white;
}
.review-list .review-list-empty {
  padding: 40px 20px;
  color: #666;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px dashed #ddd;
  margin-top: 40px;
}
.review-list .review-list-empty p {
  margin: 0;
  font-style: italic;
  font-size: 22px;
  text-align: center;
}
.review-list .review-list-controls {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}
.review-list .review-list-controls .load-more-btn,
.review-list .review-list-controls .show-less-btn {
  padding: 10px 20px;
  border: 1px solid #007bff;
  background: white;
  color: #007bff;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}
.review-list .review-list-controls .load-more-btn:hover,
.review-list .review-list-controls .show-less-btn:hover {
  background: #007bff;
  color: white;
}
.review-list .review-list-controls .show-less-btn {
  border-color: #6c757d;
  color: #6c757d;
}
.review-list .review-list-controls .show-less-btn:hover {
  background: #6c757d;
  color: white;
}
.review-list .review-summary {
  text-align: center;
  margin-top: 16px;
}
.review-list .review-summary .review-count-summary {
  color: #666;
  font-size: 14px;
  margin: 0;
}
@media (max-width: 768px) {
  .review-list .review-list-header .review-list-title {
    font-size: 18px;
  }
  .review-list .review-list-header .review-filters .rating-filter {
    gap: 6px;
  }
  .review-list .review-list-header .review-filters .rating-filter .filter-btn {
    padding: 5px 10px;
    font-size: 12px;
  }
  .review-list .review-list-controls {
    flex-direction: column;
    align-items: center;
  }
  .review-list .review-list-controls .load-more-btn,
  .review-list .review-list-controls .show-less-btn {
    width: 100%;
    max-width: 280px;
  }
}

#trustpilot-reviewskit-body {
  margin-right: 20px;
}
#trustpilot-reviewskit-body nav {
  background: white;
  margin-top: 20px;
  padding: 0 20px;
}
#trustpilot-reviewskit-body nav ul {
  display: flex;
  -moz-column-gap: 20px;
       column-gap: 20px;
  padding: 20px 10px;
  align-items: center;
  margin: 0;
}
#trustpilot-reviewskit-body nav ul li {
  color: black;
  cursor: pointer;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 0px;
}
#trustpilot-reviewskit-body nav ul li.active {
  color: #007bff;
}
#trustpilot-reviewskit-body .page {
  margin-top: 20px;
}
#trustpilot-reviewskit-body .page.review-page > .review-fetch .button-container {
  /* margin-top: 10px; */
  display: flex;
  -moz-column-gap: 10px;
       column-gap: 10px;
}
#trustpilot-reviewskit-body .page.review-page > .review-fetch .button-container > button {
  margin-top: 10px;
}
#trustpilot-reviewskit-body .page.review-page .review-page-details {
  border-radius: 8px;
  overflow: hidden;
  background-color: #fff;
  border-radius: 5px;
  /* padding: 20px; */
  margin-top: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: 150px;
  display: flex;
  -moz-column-gap: 20px;
       column-gap: 20px;
}
#trustpilot-reviewskit-body .page.review-page .review-page-details > .image-wrapper {
  width: 150px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
}
#trustpilot-reviewskit-body .page.review-page .review-page-details > .image-wrapper > img {
  width: 100%;
  -o-object-fit: contain;
     object-fit: contain;
}
#trustpilot-reviewskit-body .page.review-page .review-page-details > .business-details h3 {
  font-size: 22px;
  margin-bottom: 5px;
}
#trustpilot-reviewskit-body .page.review-page .review-page-details > .business-details p {
  margin: 0;
}
#trustpilot-reviewskit-body .page.review-page .review-data {
  margin-top: 20px;
}
#trustpilot-reviewskit-body .page.review-page .review-data .cardlist .card {
  padding: 0;
}
#trustpilot-reviewskit-body .page.review-page .review-data .cardlist .review-header {
  display: flex;
  -moz-column-gap: 10px;
       column-gap: 10px;
  border-bottom: 1px solid gray;
  padding: 10px;
}
#trustpilot-reviewskit-body .page.review-page .review-data .cardlist .review-header > .image-wrapper img {
  width: 50px;
  height: 50px;
}
#trustpilot-reviewskit-body .page.review-page .review-data .cardlist .review-header > .name-wrapper > * {
  margin: 0;
}
#trustpilot-reviewskit-body .page.review-page .review-data .cardlist .review-content {
  padding: 10px;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-header {
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-header h2 {
  font-size: 24px;
  margin-bottom: 10px;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-header p {
  color: #666;
  margin-bottom: 20px;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-header .shortcodes-search {
  max-width: 400px;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-header .shortcodes-search > div {
  margin-bottom: 0;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid .shortcode-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  transition: box-shadow 0.2s ease;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid .shortcode-card.micro_combo {
  width: 600px;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid .shortcode-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid .shortcode-card .components-card__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid .shortcode-card .components-card__header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid .shortcode-card .components-card__header .shortcode-category {
  background: #007bff;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 500;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid .shortcode-card .components-card__body {
  padding: 20px;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid .shortcode-card .components-card__body .shortcode-preview {
  margin-bottom: 16px;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid .shortcode-card .components-card__body .shortcode-preview .preview-component {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 16px;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  @import "../../../assets/css/shortcodes.css";
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid .shortcode-card .components-card__body .shortcode-preview .preview-placeholder {
  background: #f8f9fa;
  border: 2px dashed #ddd;
  border-radius: 6px;
  padding: 20px;
  text-align: center;
  color: #666;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid .shortcode-card .components-card__body .shortcode-preview .preview-placeholder span {
  font-size: 24px;
  display: block;
  margin-bottom: 8px;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid .shortcode-card .components-card__body .shortcode-preview .preview-placeholder small {
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid .shortcode-card .components-card__body .shortcode-description {
  color: #555;
  margin-bottom: 16px;
  line-height: 1.5;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid .shortcode-card .components-card__body .shortcode-code {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid .shortcode-card .components-card__body .shortcode-code code {
  flex: 1;
  background: none;
  padding: 0;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 13px;
  color: #d63384;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid .shortcode-card .components-card__body .shortcode-code .components-button.copied {
  background: #28a745;
  color: white;
  border-color: #28a745;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid .shortcode-card .components-card__body .shortcode-attributes summary {
  cursor: pointer;
  font-weight: 500;
  margin-bottom: 10px;
  color: #007bff;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid .shortcode-card .components-card__body .shortcode-attributes summary:hover {
  text-decoration: underline;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid .shortcode-card .components-card__body .shortcode-attributes .attributes-list {
  padding-left: 16px;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid .shortcode-card .components-card__body .shortcode-attributes .attributes-list .attribute-item {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid .shortcode-card .components-card__body .shortcode-attributes .attributes-list .attribute-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid .shortcode-card .components-card__body .shortcode-attributes .attributes-list .attribute-item strong {
  color: #333;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 13px;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid .shortcode-card .components-card__body .shortcode-attributes .attributes-list .attribute-item .attribute-default {
  color: #666;
  font-size: 12px;
  margin-left: 8px;
}
#trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid .shortcode-card .components-card__body .shortcode-attributes .attributes-list .attribute-item p {
  margin: 4px 0 0 0;
  color: #555;
  font-size: 13px;
}
#trustpilot-reviewskit-body .page.shortcodes-page .no-results {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}
#trustpilot-reviewskit-body .page.shortcodes-page .no-results p {
  margin: 0;
  font-style: italic;
}
@media (max-width: 768px) {
  #trustpilot-reviewskit-body .page.shortcodes-page .shortcodes-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  #trustpilot-reviewskit-body .page.shortcodes-page .shortcode-card .components-card__header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  #trustpilot-reviewskit-body .page.shortcodes-page .shortcode-card .shortcode-code {
    flex-direction: column;
    align-items: stretch;
  }
  #trustpilot-reviewskit-body .page.shortcodes-page .shortcode-card .shortcode-code .components-button {
    width: 100%;
  }
}

/*# sourceMappingURL=trustpilot-reviewskit.core.min.css.map*/