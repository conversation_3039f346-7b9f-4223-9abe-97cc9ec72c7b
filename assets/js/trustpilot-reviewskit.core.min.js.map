{"version": 3, "file": "trustpilot-reviewskit.core.min.js", "mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;;;;;;;;;;;;;;ACA0B;AACU;AACoB;AACZ;AACN;AAAA;AAEtC,MAAMS,GAAG,GAAGA,CAAA,KAAM;EAChB,MAAM;IAAEC;EAAI,CAAC,GAAGR,qEAAc,CAAC,CAAC;EAChC,oBACEM,uDAAA;IAAAG,QAAA,gBACEL,sDAAA,CAACL,qDAAM,IAAE,CAAC,EACTS,GAAG,KAAK,SAAS,iBAAIJ,sDAAA,CAACF,sDAAO,IAAE,CAAC,EAChCM,GAAG,KAAK,YAAY,iBAAIJ,sDAAA,CAACH,yDAAU,IAAE,CAAC;EAAA,CACpC,CAAC;AAEV,CAAC;AAED,iEAAeM,GAAG,E;;;;;;;;;;;;;;;;;ACjBmC;AAAA;AAErD,MAAMI,WAAW,GAAGA,CAAC;EAACC,IAAI,GAAC,EAAE;EAAE,GAAGC;AAAI,CAAC,KAAK;EACxC,IAAID,IAAI,KAAK,OAAO,EAAG;IACnB,oBACIR,sDAAA;MACIU,GAAG,EAAEJ,0DAAe,GAAG,sCAAuC;MAC9DK,GAAG,EAAC,iBAAiB;MACrBC,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAO;IAAE,CAC5B,CAAC;EAEV;EAEA,oBACIb,sDAAA;IACIU,GAAG,EAAEJ,0DAAe,GAAG,gCAAiC;IACxDK,GAAG,EAAC,iBAAiB;IAAA,GACjBF;EAAI,CACX,CAAC;AAEV,CAAC;AAED,iEAAeF,WAAW,E;;;;;;;;;;;;;;;;;;ACtBA;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA;AAQA,MAAMO,YAAY,GAAGA,CAAC;EAAEC,QAAQ,GAAG,CAAC,CAAC;EAAEC,IAAI,GAAG,QAAQ;EAAEC,SAAS,GAAG;AAAK,CAAC,KAAK;EAC7E,MAAM;IAAEC,IAAI,GAAG,WAAW;IAAEC,KAAK,GAAG;EAAG,CAAC,GAAGJ,QAAQ;;EAEnD;EACA,MAAMK,WAAW,GAAIF,IAAI,IAAK;IAC5B,IAAI,CAACA,IAAI,IAAIA,IAAI,KAAK,UAAU,EAAE,OAAO,GAAG;IAC5C,OAAOA,IAAI,CACRG,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,CAC7BC,IAAI,CAAC,EAAE,CAAC,CACRC,WAAW,CAAC,CAAC,CACbC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB,CAAC;EAED,oBACEzB,uDAAA;IAAK0B,SAAS,EAAE,gCAAgCZ,IAAI,EAAG;IAAAX,QAAA,GACpDY,SAAS,iBACRf,uDAAA;MAAK0B,SAAS,EAAC,iBAAiB;MAAAvB,QAAA,GAC7Bc,KAAK,gBACJnB,sDAAA;QACEU,GAAG,EAAES,KAAM;QACXR,GAAG,EAAE,GAAGO,IAAI,YAAa;QACzBU,SAAS,EAAC,gBAAgB;QAC1BC,OAAO,EAAGC,CAAC,IAAK;UACdA,CAAC,CAACC,MAAM,CAACnB,KAAK,CAACoB,OAAO,GAAG,MAAM;UAC/BF,CAAC,CAACC,MAAM,CAACE,WAAW,CAACrB,KAAK,CAACoB,OAAO,GAAG,MAAM;QAC7C;MAAE,CACH,CAAC,GACA,IAAI,eACRhC,sDAAA;QACE4B,SAAS,EAAC,mBAAmB;QAC7BhB,KAAK,EAAE;UAAEoB,OAAO,EAAEb,KAAK,GAAG,MAAM,GAAG;QAAO,CAAE;QAAAd,QAAA,EAE3Ce,WAAW,CAACF,IAAI;MAAC,CACf,CAAC;IAAA,CACH,CACN,eACDlB,sDAAA;MAAK4B,SAAS,EAAC,kBAAkB;MAAAvB,QAAA,eAC/BL,sDAAA;QAAM4B,SAAS,EAAC,eAAe;QAAAvB,QAAA,EAAEa;MAAI,CAAO;IAAC,CAC1C,CAAC;EAAA,CACH,CAAC;AAEV,CAAC;AAED,iEAAeJ,YAAY,E;;;;;;;;;;;;;;;;;;;;;ACtDD;AACY;AACI;AAC0B;;AAEpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;AAWA,MAAMuB,UAAU,GAAGA,CAAC;EAAEC,MAAM,GAAG,CAAC;AAAE,CAAC,KAAK;EACvC,MAAM;IACLC,QAAQ;IACRC,MAAM,GAAG,CAAC;IACVC,WAAW,GAAG,EAAE;IAChBC,UAAU,GAAG,EAAE;IACf3B,QAAQ,GAAG,CAAC,CAAC;IACb4B,KAAK,GAAG,CAAC,CAAC;IACVC,SAAS,GAAG;EACb,CAAC,GAAGN,MAAM;EAEVO,OAAO,CAACC,IAAI,CAACR,MAAM,CAAC;EAEpB,MAAM;IAAES,aAAa;IAAEC;EAAgB,CAAC,GAAGL,KAAK;;EAEhD;EACA,MAAMM,UAAU,GAAIC,UAAU,IAAK;IAClC,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;IAC1B,IAAI;MACH,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACvCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE;MACN,CAAC,CAAC;IACH,CAAC,CAAC,OAAOC,KAAK,EAAE;MACf,OAAO,EAAE;IACV;EACD,CAAC;EAED,MAAMC,aAAa,GAAGvB,4DAAa,CAACK,MAAM,CAAC;;EAE3C;EACA,MAAMmB,YAAY,GAAGA,CAACC,IAAI,EAAEC,SAAS,GAAG,GAAG,KAAK;IAC/C,IAAI,CAACD,IAAI,IAAIA,IAAI,CAACE,MAAM,IAAID,SAAS,EAAE,OAAOD,IAAI;IAClD,OAAOA,IAAI,CAACG,SAAS,CAAC,CAAC,EAAEF,SAAS,CAAC,GAAG,KAAK;EAC5C,CAAC;EAED,oBACC3D,uDAAA;IAAK0B,SAAS,EAAC,aAAa;IAAC,kBAAgBW,QAAS;IAAAlC,QAAA,gBACrDH,uDAAA;MAAK0B,SAAS,EAAC,eAAe;MAAAvB,QAAA,gBAC7BL,sDAAA,CAACc,qDAAY;QAACC,QAAQ,EAAEA,QAAS;QAACC,IAAI,EAAC;MAAO,CAAE,CAAC,eACjDd,uDAAA;QAAK0B,SAAS,EAAC,aAAa;QAAAvB,QAAA,gBAC3BL,sDAAA;UAAK4B,SAAS,EAAC,eAAe;UAAChB,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAQ,CAAE;UAAAR,QAAA,eACxDL,sDAAA;YACCU,GAAG,EAAE0B,8DAAe,CAACE,MAAM,CAACE,MAAM,CAAE;YACpC7B,GAAG,EAAE,GAAG+C,aAAa,cAAe;YACpC9C,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAO;UAAE,CACzB;QAAC,CACE,CAAC,EACLkC,aAAa,iBACb/C,sDAAA;UAAM4B,SAAS,EAAC,aAAa;UAAAvB,QAAA,EAAE4C,UAAU,CAACF,aAAa;QAAC,CAAO,CAC/D;MAAA,CACG,CAAC;IAAA,CACF,CAAC,eAEN7C,uDAAA;MAAK0B,SAAS,EAAC,gBAAgB;MAAAvB,QAAA,GAC7BoC,WAAW,iBAAIzC,sDAAA;QAAI4B,SAAS,EAAC,cAAc;QAAAvB,QAAA,EAAEoC;MAAW,CAAK,CAAC,EAC9DC,UAAU,iBACV1C,sDAAA;QAAG4B,SAAS,EAAC,aAAa;QAAAvB,QAAA,EAAEsD,YAAY,CAACjB,UAAU;MAAC,CAAI,CACxD,EACAM,eAAe,iBACfhD,sDAAA;QAAK4B,SAAS,EAAC,wBAAwB;QAAAvB,QAAA,eACtCH,uDAAA;UAAAG,QAAA,GAAO,mBAAiB,EAAC4C,UAAU,CAACD,eAAe,CAAC;QAAA,CAAQ;MAAC,CACzD,CACL;IAAA,CACG,CAAC,EAELJ,SAAS,iBACT5C,sDAAA;MAAK4B,SAAS,EAAC,eAAe;MAAAvB,QAAA,eAC7BL,sDAAA;QACCgE,IAAI,EAAEpB,SAAU;QAChBb,MAAM,EAAC,QAAQ;QACfkC,GAAG,EAAC,qBAAqB;QACzBrC,SAAS,EAAC,aAAa;QAAAvB,QAAA,EACvB;MAED,CAAG;IAAC,CACA,CACL;EAAA,CACG,CAAC;AAER,CAAC;AAED,iEAAegC,UAAU,E;;;;;;;;;;;;;;;;;;;;;ACpGe;AACF;AACD;;AAErC;AACA;AACA;AACA;AACA;AACA;AALA;AAMA,MAAM+B,UAAU,GAAGA,CAAC;EAClBC,OAAO,GAAG,CAAC,CAAC;EACZC,mBAAmB,GAAG,EAAE;EACxBC,YAAY,GAAG;AACjB,CAAC,KAAK;EACJ,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGP,+CAAQ,CAACI,mBAAmB,CAAC;;EAErE;EACA,MAAMI,YAAY,GAAGC,MAAM,CAACC,MAAM,CAACP,OAAO,CAAC,CAACQ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IACzD,MAAMC,KAAK,GAAG,IAAI5B,IAAI,CAAC0B,CAAC,CAACnC,KAAK,EAAEI,aAAa,IAAI,CAAC,CAAC;IACnD,MAAMkC,KAAK,GAAG,IAAI7B,IAAI,CAAC2B,CAAC,CAACpC,KAAK,EAAEI,aAAa,IAAI,CAAC,CAAC;IACnD,OAAOkC,KAAK,GAAGD,KAAK;EACtB,CAAC,CAAC;EAEF,MAAME,YAAY,GAAGR,YAAY,CAACZ,MAAM;EACxC,MAAMqB,gBAAgB,GAAGT,YAAY,CAAC/C,KAAK,CAAC,CAAC,EAAE6C,YAAY,CAAC;EAC5D,MAAMY,cAAc,GAAGZ,YAAY,GAAGU,YAAY;;EAElD;EACA,MAAMG,cAAc,GAAGA,CAAA,KAAM;IAC3BZ,eAAe,CAACa,IAAI,IAAIC,IAAI,CAACC,GAAG,CAACF,IAAI,GAAGhB,mBAAmB,EAAEY,YAAY,CAAC,CAAC;EAC7E,CAAC;;EAED;EACA,MAAMO,cAAc,GAAGA,CAAA,KAAM;IAC3BhB,eAAe,CAACH,mBAAmB,CAAC;EACtC,CAAC;;EAED;EACA,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,+CAAQ,CAAC,IAAI,CAAC;EAEtD,MAAM0B,eAAe,GAAGF,YAAY,GAChCP,gBAAgB,CAACU,MAAM,CAACvD,MAAM,IAAIA,MAAM,CAACE,MAAM,KAAKkD,YAAY,CAAC,GACjEP,gBAAgB;;EAEpB;EACA,MAAMW,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,YAAY,GAAG;MAAE,CAAC,EAAE,CAAC;MAAE,CAAC,EAAE,CAAC;MAAE,CAAC,EAAE,CAAC;MAAE,CAAC,EAAE,CAAC;MAAE,CAAC,EAAE;IAAE,CAAC;IACrDrB,YAAY,CAACsB,OAAO,CAAC1D,MAAM,IAAI;MAC7B,IAAIA,MAAM,CAACE,MAAM,IAAI,CAAC,IAAIF,MAAM,CAACE,MAAM,IAAI,CAAC,EAAE;QAC5CuD,YAAY,CAACR,IAAI,CAACU,KAAK,CAAC3D,MAAM,CAACE,MAAM,CAAC,CAAC,EAAE;MAC3C;IACF,CAAC,CAAC;IACF,OAAOuD,YAAY;EACrB,CAAC;EAED,MAAMG,kBAAkB,GAAGJ,qBAAqB,CAAC,CAAC;EAElD,IAAIZ,YAAY,KAAK,CAAC,EAAE;IACtB,oBACElF,sDAAA;MAAK4B,SAAS,EAAC,aAAa;MAAAvB,QAAA,eAC1BL,sDAAA;QAAK4B,SAAS,EAAC,mBAAmB;QAAAvB,QAAA,eAChCL,sDAAA;UAAAK,QAAA,EAAI8D,mDAAE,CAAC,uBAAuB,EAAE,WAAW;QAAC,CAAI;MAAC,CAC9C;IAAC,CACH,CAAC;EAEV;EAEA,oBACEjE,uDAAA;IAAK0B,SAAS,EAAC,aAAa;IAAAvB,QAAA,gBAC1BH,uDAAA;MAAK0B,SAAS,EAAC,oBAAoB;MAAAvB,QAAA,gBACjCH,uDAAA;QAAI0B,SAAS,EAAC,mBAAmB;QAAAvB,QAAA,GAAC,oBACd,EAAC6E,YAAY,EAAC,GAClC;MAAA,CAAI,CAAC,eAGLlF,sDAAA;QAAK4B,SAAS,EAAC,gBAAgB;QAAAvB,QAAA,eAC7BH,uDAAA;UAAK0B,SAAS,EAAC,eAAe;UAAAvB,QAAA,gBAC5BL,sDAAA;YACE4B,SAAS,EAAE,cAAc8D,YAAY,KAAK,IAAI,GAAG,QAAQ,GAAG,EAAE,EAAG;YACjES,OAAO,EAAEA,CAAA,KAAMR,eAAe,CAAC,IAAI,CAAE;YAAAtF,QAAA,EACtC;UAED,CAAQ,CAAC,EACR,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACiB,GAAG,CAACkB,MAAM,iBACzBtC,uDAAA;YAEE0B,SAAS,EAAE,cAAc8D,YAAY,KAAKlD,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;YACnE2D,OAAO,EAAEA,CAAA,KAAMR,eAAe,CAACnD,MAAM,CAAE;YAAAnC,QAAA,GAEtCmC,MAAM,EAAC,UAAG,EAAC0D,kBAAkB,CAAC1D,MAAM,CAAC,EAAC,GACzC;UAAA,GALOA,MAKC,CACT,CAAC;QAAA,CACC;MAAC,CACH,CAAC;IAAA,CACH,CAAC,eAENxC,sDAAA;MAAK4B,SAAS,EAAC,qBAAqB;MAAAvB,QAAA,EACjCuF,eAAe,CAAC9B,MAAM,KAAK,CAAC,gBAC3B9D,sDAAA;QAAK4B,SAAS,EAAC,mBAAmB;QAAAvB,QAAA,eAChCL,sDAAA;UAAAK,QAAA,EAAG;QAAwC,CAAG;MAAC,CAC5C,CAAC,gBAENL,sDAAA;QAAK4B,SAAS,EAAC,cAAc;QAAAvB,QAAA,EAC1BuF,eAAe,CAACtE,GAAG,CAAEgB,MAAM,iBAC1BtC,sDAAA,CAACqC,mDAAU;UAETC,MAAM,EAAEA;QAAO,GADVA,MAAM,CAACC,QAEb,CACF;MAAC,CACC;IACN,CACE,CAAC,EAGLgC,YAAY,IAAImB,YAAY,KAAK,IAAI,iBACpCxF,uDAAA;MAAK0B,SAAS,EAAC,sBAAsB;MAAAvB,QAAA,GAClC+E,cAAc,iBACblF,uDAAA;QACE0B,SAAS,EAAC,eAAe;QACzBuE,OAAO,EAAEd,cAAe;QAAAhF,QAAA,GACzB,qBACoB,EAAC6E,YAAY,GAAGV,YAAY,EAAC,aAClD;MAAA,CAAQ,CACT,EAEAA,YAAY,GAAGF,mBAAmB,iBACjCtE,sDAAA;QACE4B,SAAS,EAAC,eAAe;QACzBuE,OAAO,EAAEV,cAAe;QAAApF,QAAA,EACzB;MAED,CAAQ,CACT;IAAA,CACE,CACN,eAGDL,sDAAA;MAAK4B,SAAS,EAAC,gBAAgB;MAAAvB,QAAA,eAC7BH,uDAAA;QAAG0B,SAAS,EAAC,sBAAsB;QAAAvB,QAAA,GAAC,UAC1B,EAACuF,eAAe,CAAC9B,MAAM,EAAC,MAAI,EAACoB,YAAY,EAAC,UACpD;MAAA,CAAG;IAAC,CACD,CAAC;EAAA,CACH,CAAC;AAEV,CAAC;AAED,iEAAed,UAAU,E;;;;;;;;;;;;;;;;;;ACnJC;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA;AAQA,MAAMlC,UAAU,GAAGA,CAAC;EAAEM,MAAM,GAAG,CAAC;EAAE4D,QAAQ,GAAG,CAAC;EAAEpF,IAAI,GAAG,QAAQ;EAAEqF,UAAU,GAAG,KAAK;EAAEC,MAAM,GAAG;AAAM,CAAC,KAAK;EACxG,MAAMC,KAAK,GAAG,EAAE;EAChB,MAAMC,SAAS,GAAGjB,IAAI,CAACU,KAAK,CAACzD,MAAM,CAAC;EACpC,MAAMiE,WAAW,GAAGjE,MAAM,GAAG,CAAC,KAAK,CAAC;;EAEpC;EACA,MAAMkE,kBAAkB,GAAIlE,MAAM,IAAK;IACrC,IAAIA,MAAM,IAAI,CAAC,EAAE,OAAO,SAAS,CAAC,CAAC;IACnC,IAAIA,MAAM,IAAI,CAAC,EAAE,OAAO,SAAS,CAAC,CAAC;IACnC,IAAIA,MAAM,IAAI,CAAC,EAAE,OAAO,SAAS,CAAC,CAAC;IACnC,IAAIA,MAAM,IAAI,CAAC,EAAE,OAAO,SAAS,CAAC,CAAC;IACnC,OAAO,SAAS,CAAC,CAAC;EACpB,CAAC;EAED,MAAMmE,SAAS,GAAGD,kBAAkB,CAAClE,MAAM,CAAC;;EAE5C;EACA,MAAMoE,iBAAiB,GAAGA,CAAC;IAAEC,MAAM;IAAEC;EAAM,CAAC,kBAC1C9G,sDAAA;IACE4B,SAAS,EAAE,YAAYiF,MAAM,GAAG,aAAa,GAAG,YAAY,EAAG;IAC/DE,OAAO,EAAC,WAAW;IACnBnG,KAAK,EAAE;MAAEoG,IAAI,EAAEH,MAAM,GAAGC,KAAK,GAAG;IAAO,CAAE;IAAAzG,QAAA,eAEzCL,sDAAA;MAAMiH,CAAC,EAAC;IAAsG,CAAE;EAAC,CAC9G,CACN;;EAED;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,SAAS,EAAEU,CAAC,EAAE,EAAE;IAClCX,KAAK,CAACY,IAAI,CACRb,MAAM,gBACJtG,sDAAA,CAAC4G,iBAAiB;MAEhBC,MAAM,EAAE,IAAK;MACbC,KAAK,EAAEH;IAAU,GAFZ,QAAQO,CAAC,EAGf,CAAC,gBAEFlH,sDAAA;MAEE4B,SAAS,EAAC,gBAAgB;MAC1BhB,KAAK,EAAE;QAAEkG,KAAK,EAAEH;MAAU,CAAE;MAAAtG,QAAA,EAC7B;IAED,GALO,QAAQ6G,CAAC,EAKV,CAEV,CAAC;EACH;;EAEA;EACA,IAAIT,WAAW,IAAID,SAAS,GAAGJ,QAAQ,EAAE;IACvCG,KAAK,CAACY,IAAI,CACRb,MAAM,gBACJpG,uDAAA;MAAgB0B,SAAS,EAAC,eAAe;MAAChB,KAAK,EAAE;QAAEwG,QAAQ,EAAE;MAAW,CAAE;MAAA/G,QAAA,gBACxEL,sDAAA,CAAC4G,iBAAiB;QAACC,MAAM,EAAE,KAAM;QAACC,KAAK,EAAC;MAAM,CAAE,CAAC,eACjD9G,sDAAA;QAAKY,KAAK,EAAE;UACVwG,QAAQ,EAAE,UAAU;UACpBC,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPzG,KAAK,EAAE,KAAK;UACZ0G,QAAQ,EAAE;QACZ,CAAE;QAAAlH,QAAA,eACAL,sDAAA,CAAC4G,iBAAiB;UAACC,MAAM,EAAE,IAAK;UAACC,KAAK,EAAEH;QAAU,CAAE;MAAC,CAClD,CAAC;IAAA,GAVC,MAWJ,CAAC,gBAEN3G,sDAAA;MAEE4B,SAAS,EAAC,gBAAgB;MAC1BhB,KAAK,EAAE;QAAEkG,KAAK,EAAEH;MAAU,CAAE;MAAAtG,QAAA,EAC7B;IAED,GALM,MAKA,CAEV,CAAC;EACH;;EAEA;EACA,MAAMmH,UAAU,GAAGpB,QAAQ,GAAGI,SAAS,IAAIC,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;EAC/D,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,UAAU,EAAEN,CAAC,EAAE,EAAE;IACnCX,KAAK,CAACY,IAAI,CACRb,MAAM,gBACJtG,sDAAA,CAAC4G,iBAAiB;MAEhBC,MAAM,EAAE,KAAM;MACdC,KAAK,EAAC;IAAM,GAFP,SAASI,CAAC,EAGhB,CAAC,gBAEFlH,sDAAA;MAAyB4B,SAAS,EAAC,iBAAiB;MAAAvB,QAAA,EAAC;IAErD,GAFW,SAAS6G,CAAC,EAEf,CAEV,CAAC;EACH;EAEA,oBACEhH,uDAAA;IAAK0B,SAAS,EAAE,kDAAkDZ,IAAI,EAAG;IAAAX,QAAA,gBACvEL,sDAAA;MAAK4B,SAAS,EAAC,OAAO;MAAAvB,QAAA,EAAEkG;IAAK,CAAM,CAAC,EACnCF,UAAU,iBAAInG,uDAAA;MAAM0B,SAAS,EAAC,cAAc;MAAAvB,QAAA,GAAC,GAAC,EAACmC,MAAM,EAAC,GAAC;IAAA,CAAM,CAAC;EAAA,CAC5D,CAAC;AAEV,CAAC;AAED,iEAAeN,UAAU,E;;;;;;;;;;;;;;;;;;;AChH2C;AAC/B;;AAErC;AACA;AACA;AACA;AAHA;AAIA,MAAMuF,sBAAsB,GAAGA,CAAC;EAAEC,eAAe,GAAG,CAAC;AAAE,CAAC,KAAK;EAC5D,MAAM;IACLC,WAAW,GAAG,EAAE;IAChBC,eAAe,GAAG,EAAE;IACpBC,eAAe,GAAG,CAAC;IACnBC,UAAU,GAAG,CAAC;IACdC,UAAU,GAAG,EAAE;IACfC,eAAe,GAAG,EAAE;IACpBzB,KAAK,GAAG,CAAC;IACT0B,UAAU,GAAG,EAAE;IACfC,QAAQ,GAAG,CAAC;EACb,CAAC,GAAGR,eAAe;;EAEnB;EACA,MAAMS,YAAY,GAAIC,GAAG,IAAK;IAC7B,IAAIA,GAAG,IAAI,OAAO,EAAE;MACnB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IACxC,CAAC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACvB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IACrC;IACA,OAAOD,GAAG,CAACE,QAAQ,CAAC,CAAC;EACtB,CAAC;EAED,MAAMC,eAAe,GAAGN,UAAU,CAACO,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,SAAS,CAAC,EAAExH,IAAI,IAAI,EAAE;EAE3E,MAAMyH,UAAU,GAAGT,QAAQ,EAAEU,YAAY,EAAEC,oBAAoB,IAAI,KAAK;EACxE,MAAMC,SAAS,GAAGZ,QAAQ,EAAEY,SAAS,IAAI,KAAK;EAE9C,MAAMpF,aAAa,GAAGvB,4DAAa,CAAC2F,UAAU,CAAC;;EAE/C;EACA,MAAMiB,WAAW,GAAIC,GAAG,IAAK;IAC5B,IAAI,CAACA,GAAG,EAAE,OAAO,EAAE;IACnB,IAAIA,GAAG,CAACC,UAAU,CAAC,IAAI,CAAC,EAAE;MACzB,OAAO,SAASD,GAAG,EAAE;IACtB;IACA,OAAOA,GAAG;EACX,CAAC;EAED,IAAI,CAACrB,WAAW,EAAE;IACjB,oBACC3H,sDAAA;MAAK4B,SAAS,EAAC,0BAA0B;MAAAvB,QAAA,eACxCL,sDAAA;QAAK4B,SAAS,EAAC,sBAAsB;QAAAvB,QAAA,eACpCL,sDAAA;UAAAK,QAAA,EAAI8D,mDAAE,CAAC,2BAA2B,EAAE,WAAW;QAAC,CAAI;MAAC,CACjD;IAAC,CACF,CAAC;EAER;EAEAtB,OAAO,CAACC,IAAI,CAACoF,QAAQ,CAACgB,aAAa,CAAC;EACpC,oBACChJ,uDAAA;IAAK0B,SAAS,EAAC,0BAA0B;IAAAvB,QAAA,gBACxCH,uDAAA;MAAK0B,SAAS,EAAC,iBAAiB;MAAAvB,QAAA,gBAC/BH,uDAAA;QAAK0B,SAAS,EAAC,eAAe;QAAAvB,QAAA,GAC5B2H,eAAe,gBACfhI,sDAAA;UACCU,GAAG,EAAEqI,WAAW,CAACf,eAAe,CAAE;UAClCrH,GAAG,EAAE,GAAGgH,WAAW,OAAQ;UAC3B/F,SAAS,EAAC,gBAAgB;UAC1BC,OAAO,EAAGC,CAAC,IAAK;YACfA,CAAC,CAACC,MAAM,CAACnB,KAAK,CAACoB,OAAO,GAAG,MAAM;YAC/BF,CAAC,CAACC,MAAM,CAACE,WAAW,CAACrB,KAAK,CAACoB,OAAO,GAAG,MAAM;UAC5C;QAAE,CACF,CAAC,GACC,IAAI,eACRhC,sDAAA;UACC4B,SAAS,EAAC,mBAAmB;UAC7BhB,KAAK,EAAE;YAAEoB,OAAO,EAAEgG,eAAe,GAAG,MAAM,GAAG;UAAO,CAAE;UAAA3H,QAAA,EAErDsH,WAAW,CAACnG,MAAM,CAAC,CAAC,CAAC,CAACE,WAAW,CAAC;QAAC,CAChC,CAAC;MAAA,CACF,CAAC,eAENxB,uDAAA;QAAK0B,SAAS,EAAC,eAAe;QAAAvB,QAAA,gBAC7BH,uDAAA;UAAK0B,SAAS,EAAC,uBAAuB;UAAAvB,QAAA,gBACrCL,sDAAA;YAAI4B,SAAS,EAAC,eAAe;YAAAvB,QAAA,EAAEsH;UAAW,CAAK,CAAC,EAC/CmB,SAAS,iBACT5I,uDAAA;YAAM0B,SAAS,EAAC,kBAAkB;YAAAvB,QAAA,GAAC,SAChC,EAAC8D,mDAAE,CAAC,SAAS,EAAE,WAAW,CAAC;UAAA,CACxB,CACN,EACAwE,UAAU,iBACVzI,uDAAA;YAAM0B,SAAS,EAAC,mBAAmB;YAAAvB,QAAA,GAAC,SACjC,EAAC8D,mDAAE,CAAC,UAAU,EAAE,WAAW,CAAC;UAAA,CACzB,CACN;QAAA,CACG,CAAC,EAELyD,eAAe,iBACf5H,sDAAA;UAAG4B,SAAS,EAAC,kBAAkB;UAAAvB,QAAA,EAAEuH;QAAe,CAAI,CACpD,EAEAW,eAAe,iBACfvI,sDAAA;UAAG4B,SAAS,EAAC,mBAAmB;UAAAvB,QAAA,EAAEkI;QAAe,CAAI,CACrD,eAEDrI,uDAAA;UAAK0B,SAAS,EAAC,iBAAiB;UAAAvB,QAAA,gBAC/BH,uDAAA;YAAK0B,SAAS,EAAC,mBAAmB;YAAAvB,QAAA,gBACjCL,sDAAA;cAAK4B,SAAS,EAAC,eAAe;cAAChB,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAQ,CAAE;cAAAR,QAAA,eACxDL,sDAAA;gBACCU,GAAG,EAAE0B,8DAAe,CAAC0F,UAAU,CAAE;gBACjCnH,GAAG,EAAE,GAAG+C,aAAa;cAAe,CACpC;YAAC,CACE,CAAC,eACN1D,sDAAA;cAAAK,QAAA,EAAOyH;YAAU,CAAO,CAAC;UAAA,CACrB,CAAC,eACN5H,uDAAA;YAAM0B,SAAS,EAAC,cAAc;YAAAvB,QAAA,GAC5B8D,mDAAE,CAAC,UAAU,EAAE,WAAW,CAAC,EAAC,GAAC,EAACgE,YAAY,CAACN,eAAe,CAAC,EAAE,GAAG,EAChE1D,mDAAE,CAAC,SAAS,EAAE,WAAW,CAAC;UAAA,CACtB,CAAC;QAAA,CACH,CAAC,EAEL4D,UAAU,iBACV/H,sDAAA;UAAK4B,SAAS,EAAC,gBAAgB;UAAAvB,QAAA,eAC9BL,sDAAA;YACCgE,IAAI,EAAE+D,UAAW;YACjBhG,MAAM,EAAC,QAAQ;YACfkC,GAAG,EAAC,qBAAqB;YACzBrC,SAAS,EAAC,sDAAsD;YAAAvB,QAAA,EAE/D8D,mDAAE,CAAC,eAAe,EAAE,WAAW;UAAC,CAC/B;QAAC,CACA,CACL;MAAA,CACG,CAAC;IAAA,CACF,CAAC,EAEL+D,QAAQ,EAAEgB,aAAa,iBACvBhJ,uDAAA;MAAK0B,SAAS,EAAC,gBAAgB;MAAAvB,QAAA,gBAC9BH,uDAAA;QAAK0B,SAAS,EAAC,WAAW;QAAAvB,QAAA,gBACzBH,uDAAA;UAAM0B,SAAS,EAAC,YAAY;UAAAvB,QAAA,GAAE8D,mDAAE,CAAC,YAAY,EAAE,WAAW,CAAC,EAAC,GAAC;QAAA,CAAM,CAAC,eACpEjE,uDAAA;UAAM0B,SAAS,EAAC,YAAY;UAAAvB,QAAA,GAC1B6H,QAAQ,EAAEgB,aAAa,EAAEC,eAAe,EAAEd,OAAO,CAAC,CAAC,CAAC,EAAC,GACvD;QAAA,CAAM,CAAC;MAAA,CACH,CAAC,EACLH,QAAQ,CAACgB,aAAa,EAAEE,kBAAkB,IAAI,CAAC,iBAC/ClJ,uDAAA;QAAK0B,SAAS,EAAC,WAAW;QAAAvB,QAAA,gBACzBH,uDAAA;UAAM0B,SAAS,EAAC,YAAY;UAAAvB,QAAA,GAC1B8D,mDAAE,CAAC,iBAAiB,EAAE,WAAW,CAAC,EAAC,GACrC;QAAA,CAAM,CAAC,eACPjE,uDAAA;UAAM0B,SAAS,EAAC,YAAY;UAAAvB,QAAA,GAC1BkF,IAAI,CAAC8D,KAAK,CAACnB,QAAQ,CAACgB,aAAa,CAACE,kBAAkB,CAAC,EAAE,GAAG,EAC1DjF,mDAAE,CAAC,MAAM,EAAE,WAAW,CAAC;QAAA,CACnB,CAAC;MAAA,CACH,CACL;IAAA,CACG,CACL;EAAA,CACG,CAAC;AAER,CAAC;AAED,iEAAesD,sBAAsB,E;;;;;;;;;;;;;;;;;;;;AC/J8B;AACnE,MAAM+B,WAAW,GAAGF,oDAAa,CAAC,CAAC;AACqB;AAAA;AAExD,SAASK,mBAAmBA,CAAC;EAAEtJ;AAAS,CAAC,EAAE;EACzC,MAAM,CAACD,GAAG,EAAEwJ,MAAM,CAAC,GAAG1F,+CAAQ,CAAC,SAAS,CAAC;EACzC,MAAM,CAACuF,IAAI,EAAEI,OAAO,CAAC,GAAG3F,+CAAQ,CAACwF,+CAAa,IAAI,CAAC,CAAC,CAAC;EAErD,MAAMI,KAAK,GAAG;IACZ1J,GAAG;IACHwJ,MAAM;IACNH,IAAI;IACJI;EACF,CAAC;EAED,oBAAO7J,sDAAA,CAACwJ,WAAW,CAACO,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAzJ,QAAA,EAAEA;EAAQ,CAAuB,CAAC;AAC9E;AAEA,SAAST,cAAcA,CAAA,EAAG;EACxB,OAAO2J,iDAAU,CAACC,WAAW,CAAC;AAChC;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpBO,MAAM;EACXQ,OAAO;EACPC,KAAK;EACL3J,eAAe;EACfmJ,IAAI;EACJS,KAAK;EACLC,YAAY;EACZC,MAAM;EACNC;AACF,CAAC,GAAGC,MAAM,CAACC,oBAAoB;AAC/B;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMC,iBAAiB,GAAG,MAAAA,CAAOJ,MAAM,EAAEK,OAAO,GAAG,CAAC,CAAC,KAAK;EAC/D,IAAI,CAACL,MAAM,EAAE;IACX,MAAM,IAAIM,KAAK,CAAC,oBAAoB,CAAC;EACvC;;EAEA;EACA,MAAMC,WAAW,GAAGP,MAAM,CACvBQ,OAAO,CAAC,yBAAyB,EAAE,EAAE,CAAC,CACtCA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EAErB,MAAM;IAAEC,OAAO,GAAG,KAAK;IAAEC;EAAO,CAAC,GAAGL,OAAO;EAE3C,IAAI;IACF;IACA,MAAMM,UAAU,GAAGD,MAAM,GAAG,IAAI,GAAG,IAAIE,eAAe,CAAC,CAAC;IACxD,MAAMC,SAAS,GAAGH,MAAM,GACpB,IAAI,GACJI,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAEN,OAAO,CAAC;IAEjD,MAAMO,QAAQ,GAAG,MAAMC,KAAK,CAC1B,yDAAyDC,kBAAkB,CACzEX,WACF,CAAC,EAAE,EACH;MACEY,MAAM,EAAE,KAAK;MACbC,OAAO,EAAE;QACPC,MAAM,EAAE,kBAAkB;QAC1B,cAAc,EAAE;MAClB,CAAC;MACDX,MAAM,EAAEA,MAAM,IAAIC,UAAU,CAACD;IAC/B,CACF,CAAC;;IAED;IACA,IAAIG,SAAS,EAAE;MACbS,YAAY,CAACT,SAAS,CAAC;IACzB;IAEA,IAAI,CAACG,QAAQ,CAACO,EAAE,EAAE;MAChB,MAAM,IAAIjB,KAAK,CACb,4BAA4BU,QAAQ,CAACQ,MAAM,IAAIR,QAAQ,CAACS,UAAU,EACpE,CAAC;IACH;IAEA,MAAMpC,IAAI,GAAG,MAAM2B,QAAQ,CAACU,IAAI,CAAC,CAAC;IAClC,OAAOrC,IAAI;EACb,CAAC,CAAC,OAAOhG,KAAK,EAAE;IACd,IAAIA,KAAK,CAACvC,IAAI,KAAK,YAAY,EAAE;MAC/B,MAAM,IAAIwJ,KAAK,CAAC,mBAAmB,CAAC;IACtC;IACA,MAAMjH,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMsI,UAAU,GAAGA,CAAC3B,MAAM,EAAEK,OAAO,GAAG,CAAC,CAAC,KAAK;EAClD,IAAI,CAACL,MAAM,EAAE;IACX,OAAO4B,OAAO,CAACC,MAAM,CAAC,IAAIvB,KAAK,CAAC,oBAAoB,CAAC,CAAC;EACxD;EAEA,MAAM;IAAEG,OAAO,GAAG;EAAM,CAAC,GAAGJ,OAAO;;EAEnC;EACA,IACE,OAAOH,MAAM,CAACN,OAAO,KAAK,WAAW,IACrC,OAAOM,MAAM,CAACC,oBAAoB,KAAK,WAAW,EAClD;IACA,OAAOyB,OAAO,CAACC,MAAM,CACnB,IAAIvB,KAAK,CACP,0EACF,CACF,CAAC;EACH;EAEA,OAAO,IAAIsB,OAAO,CAAC,CAACE,OAAO,EAAED,MAAM,KAAK;IACtC;IACA,MAAME,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE,kCAAkC,CAAC;IAC7DF,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEpC,KAAK,CAAC;IAClCkC,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEjC,MAAM,CAAC;IACjC+B,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAE5B,OAAO,CAAC6B,UAAU,CAAC;;IAEjD;IACA,IAAIhC,MAAM,CAACiC,MAAM,EAAE;MACjBA,MAAM,CAACC,IAAI,CAAC;QACVxD,GAAG,EAAEsB,MAAM,CAACN,OAAO;QACnBxJ,IAAI,EAAE,MAAM;QACZiJ,IAAI,EAAE0C,QAAQ;QACdM,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE,KAAK;QAClB7B,OAAO,EAAEA,OAAO;QAChB8B,OAAO,EAAE,SAAAA,CAAUvB,QAAQ,EAAE;UAC3B,IAAIA,QAAQ,CAACuB,OAAO,EAAE;YACpBT,OAAO,CAACd,QAAQ,CAAC3B,IAAI,CAAC;UACxB,CAAC,MAAM;YACLwC,MAAM,CACJ,IAAIvB,KAAK,CAACU,QAAQ,CAAC3B,IAAI,EAAEmD,OAAO,IAAI,wBAAwB,CAC9D,CAAC;UACH;QACF,CAAC;QACDnJ,KAAK,EAAE,SAAAA,CAAUoJ,GAAG,EAAEjB,MAAM,EAAEnI,KAAK,EAAE;UACnC,IAAImI,MAAM,KAAK,SAAS,EAAE;YACxBK,MAAM,CAAC,IAAIvB,KAAK,CAAC,mBAAmB,CAAC,CAAC;UACxC,CAAC,MAAM;YACLuB,MAAM,CAAC,IAAIvB,KAAK,CAAC,wBAAwBjH,KAAK,EAAE,CAAC,CAAC;UACpD;QACF;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,MAAMsH,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;MACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAEN,OAAO,CAAC;MAE/DQ,KAAK,CAACf,MAAM,CAACN,OAAO,EAAE;QACpBuB,MAAM,EAAE,MAAM;QACduB,IAAI,EAAEX,QAAQ;QACdrB,MAAM,EAAEC,UAAU,CAACD;MACrB,CAAC,CAAC,CACCiC,IAAI,CAAE3B,QAAQ,IAAK;QAClBM,YAAY,CAACT,SAAS,CAAC;QACvB,IAAI,CAACG,QAAQ,CAACO,EAAE,EAAE;UAChB,MAAM,IAAIjB,KAAK,CACb,mBAAmBU,QAAQ,CAACQ,MAAM,KAAKR,QAAQ,CAACS,UAAU,EAC5D,CAAC;QACH;QACA,OAAOT,QAAQ,CAACU,IAAI,CAAC,CAAC;MACxB,CAAC,CAAC,CACDiB,IAAI,CAAE3B,QAAQ,IAAK;QAClB,IAAIA,QAAQ,CAACuB,OAAO,EAAE;UACpBT,OAAO,CAACd,QAAQ,CAAC3B,IAAI,CAAC;QACxB,CAAC,MAAM;UACLwC,MAAM,CACJ,IAAIvB,KAAK,CAACU,QAAQ,CAAC3B,IAAI,EAAEmD,OAAO,IAAI,wBAAwB,CAC9D,CAAC;QACH;MACF,CAAC,CAAC,CACDI,KAAK,CAAEvJ,KAAK,IAAK;QAChBiI,YAAY,CAACT,SAAS,CAAC;QACvB,IAAIxH,KAAK,CAACvC,IAAI,KAAK,YAAY,EAAE;UAC/B+K,MAAM,CAAC,IAAIvB,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACxC,CAAC,MAAM;UACLuB,MAAM,CAACxI,KAAK,CAAC;QACf;MACF,CAAC,CAAC;IACN;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACO,MAAMwJ,iBAAiB,GAAI/C,KAAK,IAAK;EAC1C,IAAIA,KAAK,IAAI,OAAO,EAAE;IACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAE7B,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;EAC3C,CAAC,MAAM,IAAI6B,KAAK,IAAI,IAAI,EAAE;IACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAE7B,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;EACxC;EACA,OAAO6B,KAAK,CAAC5B,QAAQ,CAAC,CAAC;AACzB,CAAC;AAEM,MAAMlG,eAAe,GAAII,MAAM,IAAK;EAC1C,MAAM0K,aAAa,GAAG3H,IAAI,CAAC8D,KAAK,CAAC7G,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAClD;EACA,MAAM2K,QAAQ,GAAG,SAASD,aAAa,CAAC7E,OAAO,CAAC,CAAC,CAAC,MAAM;EACxD,OAAO/H,eAAe,GAAG,iBAAiB,GAAG6M,QAAQ;AACtD,CAAC;;AAED;AACO,MAAMhL,aAAa,GAAIiL,KAAK,IAAK;EACtC,IAAIA,KAAK,IAAI,GAAG,EAAE,OAAO,WAAW;EACpC,IAAIA,KAAK,IAAI,GAAG,EAAE,OAAO,OAAO;EAChC,IAAIA,KAAK,IAAI,GAAG,EAAE,OAAO,MAAM;EAC/B,IAAIA,KAAK,IAAI,GAAG,EAAE,OAAO,SAAS;EAClC,OAAO,MAAM;AACf,CAAC,C;;;;;;;;;;;;;;;;;;;AC5MyB;AAC+B;AAAA;AACzD,MAAMzN,MAAM,GAAGA,CAAA,KAAM;EACnB,MAAM;IAAES,GAAG;IAAEwJ;EAAO,CAAC,GAAGhK,qEAAc,CAAC,CAAC;EAExC,oBACEI,sDAAA;IAAAK,QAAA,eACEL,sDAAA;MAAAK,QAAA,EACGgN,SAAS,CAAC/L,GAAG,CAAEgM,IAAI,iBAClBtN,sDAAA;QAEE4B,SAAS,EAAE,GAAGxB,GAAG,KAAKkN,IAAI,CAAClN,GAAG,GAAG,QAAQ,GAAG,EAAE,EAAG;QACjD+F,OAAO,EAAEA,CAAA,KAAMyD,MAAM,CAAC0D,IAAI,CAAClN,GAAG,CAAE;QAAAC,QAAA,EAE/BiN,IAAI,CAACC;MAAK,GAJND,IAAI,CAACC,KAKR,CACL;IAAC,CACA;EAAC,CACF,CAAC;AAEV,CAAC;AAED,iEAAe5N,MAAM,EAAC;AAEtB,MAAM0N,SAAS,GAAG,CAChB;EAAEE,KAAK,EAAE,SAAS;EAAEnN,GAAG,EAAE;AAAU,CAAC,EACpC;EAAEmN,KAAK,EAAE,YAAY;EAAEnN,GAAG,EAAE;AAAa,CAAC,CAC3C,C;;;;;;;;;;;;;;;;;;;;;;;;;;AC3BuC;AAC6B;AAChC;AAC4B;AACL;AACyB;AACxB;AAAA;AAE7D,MAAMN,OAAO,GAAGA,CAAA,KAAM;EACrB,MAAM;IAAE2J,IAAI,GAAG,CAAC,CAAC;IAAEI;EAAQ,CAAC,GAAGjK,qEAAc,CAAC,CAAC;EAC/C,MAAM;IAAEiO,gBAAgB,GAAG,CAAC,CAAC;IAAExJ,OAAO,GAAG;EAAG,CAAC,GAAGoF,IAAI;EACpD,MAAM,CAACqE,aAAa,EAAEC,gBAAgB,CAAC,GAAG7J,+CAAQ,CAACmG,0DAAe,CAAC;EACnE,MAAM,CAAC2D,OAAO,EAAEC,UAAU,CAAC,GAAG/J,+CAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMgK,cAAc,GAAG,MAAAA,CAAO5B,UAAU,GAAG,KAAK,KAAK;IACpD2B,UAAU,CAAC,IAAI,CAAC;IAChB,MAAM7C,QAAQ,GAAG,MAAMW,yDAAU,CAAC+B,aAAa,EAAE;MAAExB;IAAW,CAAC,CAAC;IAChE,IAAIlB,QAAQ,EAAE3B,IAAI,EAAE;MACnBI,OAAO,CAACuB,QAAQ,CAAC3B,IAAI,CAAC;IACvB;IACAwE,UAAU,CAAC,KAAK,CAAC;EAClB,CAAC;EAED,oBACC/N,uDAAA;IAAK0B,SAAS,EAAC,kBAAkB;IAAAvB,QAAA,gBAChCH,uDAAA;MAAK0B,SAAS,EAAC,cAAc;MAAAvB,QAAA,gBAC5BL,sDAAA,CAACwN,8DAAW;QACXW,uBAAuB;QACvBC,qBAAqB;QACrBb,KAAK,EAAEpJ,mDAAE,CAAC,gBAAgB,EAAE,sBAAsB,CAAE;QACpD3D,IAAI,EAAC,KAAK;QACV6N,IAAI,EAAElK,mDAAE,CACP,uCAAuC,EACvC,sBACD,CAAE;QACF2F,KAAK,EAAEgE,aAAc;QACrBQ,QAAQ,EAAGxE,KAAK,IAAKiE,gBAAgB,CAACjE,KAAK;MAAE,CAC7C,CAAC,eACF5J,uDAAA;QAAK0B,SAAS,EAAC,kBAAkB;QAAAvB,QAAA,gBAChCL,sDAAA,CAACyN,yDAAM;UACNc,OAAO,EAAC,SAAS;UACjB3N,KAAK,EAAE;YAAE4N,eAAe,EAAE,SAAS;YAAEC,UAAU,EAAE;UAAM,CAAE;UACzDtI,OAAO,EAAE+H,cAAe;UACxBQ,QAAQ,EAAEZ,aAAa,KAAK,EAAG;UAAAzN,QAAA,EAE9B8D,mDAAE,CAAC,eAAe,EAAE,sBAAsB;QAAC,CACrC,CAAC,eACTnE,sDAAA,CAACyN,yDAAM;UACNc,OAAO,EAAC,WAAW;UACnBpI,OAAO,EAAEA,CAAA,KAAM+H,cAAc,CAAC,IAAI,CAAE;UACpCQ,QAAQ,EAAE,CAACjF,IAAK;UAAApJ,QAAA,EAEf8D,mDAAE,CAAC,oBAAoB,EAAE,sBAAsB;QAAC,CAC1C,CAAC;MAAA,CACL,CAAC;IAAA,CACF,CAAC,EAEL6J,OAAO,gBACPhO,sDAAA;MAAKY,KAAK,EAAE;QAAE+N,SAAS,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAvO,QAAA,eACtDL,sDAAA,CAAC0N,0DAAO;QACP9M,KAAK,EAAE;UACNiO,MAAM,EAAE,gBAAgB;UACxBhO,KAAK,EAAE;QACR;MAAE,CACF;IAAC,CACE,CAAC,gBAENX,uDAAA,CAAA0N,uDAAA;MAAAvN,QAAA,gBACCL,sDAAA,CAACyH,kFAAsB;QAACC,eAAe,EAAEmG;MAAiB,CAAE,CAAC,eAC7D7N,sDAAA,CAACoE,sEAAU;QAACC,OAAO,EAAEA;MAAQ,CAAE,CAAC;IAAA,CAC/B,CACF;EAAA,CACG,CAAC;AAER,CAAC;AAED,iEAAevE,OAAO,E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5EkB;AAOT;AACM;;AAErC;AAC+E;AACM;AACA;AACZ;AACc;AACV;AACF;AACtB;AAAA;AAErD,MAAMD,UAAU,GAAGA,CAAA,KAAM;EACxB,MAAM,CAAC2P,UAAU,EAAEC,aAAa,CAAC,GAAGvL,+CAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwL,eAAe,EAAEC,kBAAkB,CAAC,GAAGzL,+CAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAM0L,UAAU,GAAG,CAClB;IACCC,EAAE,EAAE,MAAM;IACV3O,IAAI,EAAE,eAAe;IACrB4O,SAAS,EAAE,6BAA6B;IACxCC,WAAW,EACV,kEAAkE;IACnEC,QAAQ,EAAE,MAAM;IAChBC,OAAO,EAAEhB,+EAAmB;IAC5BiB,UAAU,EAAE,CACX;MACChP,IAAI,EAAE,YAAY;MAClBiP,OAAO,EAAE,KAAK;MACdJ,WAAW,EAAE;IACd,CAAC,EACD;MAAE7O,IAAI,EAAE,SAAS;MAAEiP,OAAO,EAAE,KAAK;MAAEJ,WAAW,EAAE;IAAoB,CAAC,EACrE;MAAE7O,IAAI,EAAE,KAAK;MAAEiP,OAAO,EAAE,GAAG;MAAEJ,WAAW,EAAE;IAAW,CAAC;EAExD,CAAC,EACD;IACCF,EAAE,EAAE,SAAS;IACb3O,IAAI,EAAE,kBAAkB;IACxB4O,SAAS,EAAE,gCAAgC;IAC3CC,WAAW,EAAE,iDAAiD;IAC9DC,QAAQ,EAAE,MAAM;IAChBC,OAAO,EAAEf,kFAAsB;IAC/BgB,UAAU,EAAE,CACX;MAAEhP,IAAI,EAAE,SAAS;MAAEiP,OAAO,EAAE,OAAO;MAAEJ,WAAW,EAAE;IAAoB,CAAC,EACvE;MAAE7O,IAAI,EAAE,KAAK;MAAEiP,OAAO,EAAE,GAAG;MAAEJ,WAAW,EAAE;IAAW,CAAC;EAExD,CAAC,EACD;IACCF,EAAE,EAAE,kBAAkB;IACtB3O,IAAI,EAAE,kBAAkB;IACxB4O,SAAS,EAAE,yCAAyC;IACpDC,WAAW,EAAE,qCAAqC;IAClDC,QAAQ,EAAE,MAAM;IAChBC,OAAO,EAAEd,kFAAsB;IAC/Be,UAAU,EAAE,CACX;MAAEhP,IAAI,EAAE,QAAQ;MAAEiP,OAAO,EAAE,WAAW;MAAEJ,WAAW,EAAE;IAAc,CAAC,EACpE;MAAE7O,IAAI,EAAE,OAAO;MAAEiP,OAAO,EAAE,KAAK;MAAEJ,WAAW,EAAE;IAAgB,CAAC,EAC/D;MAAE7O,IAAI,EAAE,KAAK;MAAEiP,OAAO,EAAE,GAAG;MAAEJ,WAAW,EAAE;IAAW,CAAC;EAExD,CAAC,EACD;IACCF,EAAE,EAAE,YAAY;IAChB3O,IAAI,EAAE,YAAY;IAClB4O,SAAS,EAAE,mCAAmC;IAC9CC,WAAW,EAAE,iCAAiC;IAC9CC,QAAQ,EAAE,MAAM;IAChBC,OAAO,EAAEb,4EAAgB;IACzBc,UAAU,EAAE,CACX;MAAEhP,IAAI,EAAE,QAAQ;MAAEiP,OAAO,EAAE,WAAW;MAAEJ,WAAW,EAAE;IAAc,CAAC,EACpE;MAAE7O,IAAI,EAAE,OAAO;MAAEiP,OAAO,EAAE,GAAG;MAAEJ,WAAW,EAAE;IAAwB,CAAC,EACrE;MAAE7O,IAAI,EAAE,KAAK;MAAEiP,OAAO,EAAE,GAAG;MAAEJ,WAAW,EAAE;IAAW,CAAC;EAExD,CAAC,EACD;IACCF,EAAE,EAAE,mBAAmB;IACvB3O,IAAI,EAAE,oBAAoB;IAC1B4O,SAAS,EAAE,0CAA0C;IACrDC,WAAW,EAAE,2BAA2B;IACxCC,QAAQ,EAAE,MAAM;IAChBC,OAAO,EAAEZ,mFAAuB;IAChCa,UAAU,EAAE,CACX;MAAEhP,IAAI,EAAE,OAAO;MAAEiP,OAAO,EAAE,KAAK;MAAEJ,WAAW,EAAE;IAAoB,CAAC,EACnE;MAAE7O,IAAI,EAAE,KAAK;MAAEiP,OAAO,EAAE,GAAG;MAAEJ,WAAW,EAAE;IAAW,CAAC;EAExD,CAAC,EACD;IACCF,EAAE,EAAE,cAAc;IAClB3O,IAAI,EAAE,cAAc;IACpB4O,SAAS,EAAE,qCAAqC;IAChDC,WAAW,EAAE,uCAAuC;IACpDC,QAAQ,EAAE,MAAM;IAChBC,OAAO,EAAEX,8EAAkB;IAC3BY,UAAU,EAAE,CACX;MACChP,IAAI,EAAE,SAAS;MACfiP,OAAO,EAAE,OAAO;MAChBJ,WAAW,EAAE;IACd,CAAC,EACD;MAAE7O,IAAI,EAAE,KAAK;MAAEiP,OAAO,EAAE,GAAG;MAAEJ,WAAW,EAAE;IAAW,CAAC;EAExD,CAAC,EACD;IACCF,EAAE,EAAE,aAAa;IACjB3O,IAAI,EAAE,aAAa;IACnB4O,SAAS,EAAE,oCAAoC;IAC/CC,WAAW,EAAE,8CAA8C;IAC3DC,QAAQ,EAAE,MAAM;IAChBC,OAAO,EAAEV,6EAAiB;IAC1BW,UAAU,EAAE,CACX;MAAEhP,IAAI,EAAE,QAAQ;MAAEiP,OAAO,EAAE,WAAW;MAAEJ,WAAW,EAAE;IAAc,CAAC,EACpE;MAAE7O,IAAI,EAAE,OAAO;MAAEiP,OAAO,EAAE,GAAG;MAAEJ,WAAW,EAAE;IAAwB,CAAC,EACrE;MAAE7O,IAAI,EAAE,SAAS;MAAEiP,OAAO,EAAE,KAAK;MAAEJ,WAAW,EAAE;IAAoB,CAAC,EACrE;MAAE7O,IAAI,EAAE,KAAK;MAAEiP,OAAO,EAAE,GAAG;MAAEJ,WAAW,EAAE;IAAW,CAAC;EAExD,CAAC,CACD;;EAED;EACA,MAAMK,kBAAkB,GAAGR,UAAU,CAAC/J,MAAM,CAC1CiK,SAAS,IACTA,SAAS,CAAC5O,IAAI,CAACmP,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACd,UAAU,CAACa,WAAW,CAAC,CAAC,CAAC,IAC/DP,SAAS,CAACC,WAAW,CAACM,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACd,UAAU,CAACa,WAAW,CAAC,CAAC,CAAC,IACtEP,SAAS,CAACA,SAAS,CAACO,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACd,UAAU,CAACa,WAAW,CAAC,CAAC,CACrE,CAAC;;EAED;EACA,MAAME,eAAe,GAAG,MAAOT,SAAS,IAAK;IAC5C,IAAI;MACH;MACA,IAAIU,SAAS,CAACC,SAAS,IAAInG,MAAM,CAACoG,eAAe,EAAE;QAClD,MAAMF,SAAS,CAACC,SAAS,CAACE,SAAS,CAACb,SAAS,CAAC;QAC9CH,kBAAkB,CAACG,SAAS,CAAC;QAC7B5E,UAAU,CAAC,MAAMyE,kBAAkB,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;QAC9C;MACD;;MAEA;MACA,MAAMiB,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;MACnDF,QAAQ,CAAC9G,KAAK,GAAGgG,SAAS;MAC1Bc,QAAQ,CAAChQ,KAAK,CAACwG,QAAQ,GAAG,OAAO;MACjCwJ,QAAQ,CAAChQ,KAAK,CAAC0G,IAAI,GAAG,WAAW;MACjCsJ,QAAQ,CAAChQ,KAAK,CAACyG,GAAG,GAAG,WAAW;MAChCwJ,QAAQ,CAAC/D,IAAI,CAACiE,WAAW,CAACH,QAAQ,CAAC;MACnCA,QAAQ,CAACI,KAAK,CAAC,CAAC;MAChBJ,QAAQ,CAACK,MAAM,CAAC,CAAC;MAEjB,MAAMC,UAAU,GAAGL,QAAQ,CAACM,WAAW,CAAC,MAAM,CAAC;MAC/CN,QAAQ,CAAC/D,IAAI,CAACsE,WAAW,CAACR,QAAQ,CAAC;MAEnC,IAAIM,UAAU,EAAE;QACfvB,kBAAkB,CAACG,SAAS,CAAC;QAC7B5E,UAAU,CAAC,MAAMyE,kBAAkB,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC/C,CAAC,MAAM;QACN,MAAM,IAAIjF,KAAK,CAAC,oBAAoB,CAAC;MACtC;IACD,CAAC,CAAC,OAAO2G,GAAG,EAAE;MACbxO,OAAO,CAACY,KAAK,CAAC,4BAA4B,EAAE4N,GAAG,CAAC;MAChD;MACAC,KAAK,CAAC,iCAAiCxB,SAAS,EAAE,CAAC;IACpD;EACD,CAAC;EAED,oBACC5P,wDAAA;IAAK0B,SAAS,EAAC,sBAAsB;IAAAvB,QAAA,gBACpCH,wDAAA;MAAK0B,SAAS,EAAC,mBAAmB;MAAChB,KAAK,EAAE;QAAEoB,OAAO,EAAE,MAAM;QAAEuP,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAgB,CAAE;MAAAnR,QAAA,gBACpHH,wDAAA;QAAAG,QAAA,gBACCL,uDAAA;UAAAK,QAAA,EAAK8D,mDAAE,CAAC,uBAAuB,EAAE,sBAAsB;QAAC,CAAK,CAAC,eAC9DnE,uDAAA;UAAAK,QAAA,EACE8D,mDAAE,CACF,qEAAqE,EACrE,sBACD;QAAC,CACC,CAAC;MAAA,CACA,CAAC,eAENnE,uDAAA,CAACwN,8DAAWA;MACX;MAAA;QACA1D,KAAK,EAAE0F,UAAW;QAClBlB,QAAQ,EAAEmB,aAAc;QACxBgC,WAAW,EAAEtN,mDAAE,CACd,8CAA8C,EAC9C,sBACD,CAAE;QACFvC,SAAS,EAAC;MAAmB,CAC7B,CAAC;IAAA,CACE,CAAC,eAEN5B,uDAAA;MAAK4B,SAAS,EAAC,iBAAiB;MAAAvB,QAAA,EAC9B+P,kBAAkB,CAAC9O,GAAG,CAAEwO,SAAS,iBACjC5P,wDAAA,CAAC4O,uDAAI;QAAoBlN,SAAS,EAAE,kBAAkBkO,SAAS,CAACD,EAAE,EAAG;QAAAxP,QAAA,gBACpEH,wDAAA,CAAC8O,6DAAU;UAAA3O,QAAA,gBACVL,uDAAA;YAAAK,QAAA,EAAKyP,SAAS,CAAC5O;UAAI,CAAK,CAAC,eACzBlB,uDAAA;YAAM4B,SAAS,EAAC,oBAAoB;YAAAvB,QAAA,EAAEyP,SAAS,CAACE;UAAQ,CAAO,CAAC;QAAA,CACrD,CAAC,eACb9P,wDAAA,CAAC6O,2DAAQ;UAAA1O,QAAA,gBACRL,uDAAA;YAAK4B,SAAS,EAAC,mBAAmB;YAAAvB,QAAA,EAChCyP,SAAS,CAACG,OAAO,gBACjBjQ,uDAAA;cACC4B,SAAS,EAAC,mBAAmB;cAC7BhB,KAAK,EAAE;gBAAE2G,QAAQ,EAAE;cAAU,CAAE;cAAAlH,QAAA,eAE/BL,uDAAA,CAAC8P,SAAS,CAACG,OAAO,IAAE;YAAC,CACjB,CAAC,gBAEN/P,wDAAA;cAAK0B,SAAS,EAAC,qBAAqB;cAAAvB,QAAA,gBACnCL,uDAAA;gBAAAK,QAAA,EAAM;cAAE,CAAM,CAAC,eACfL,uDAAA;gBAAAK,QAAA,EAAQ8D,mDAAE,CAAC,SAAS,EAAE,sBAAsB;cAAC,CAAQ,CAAC;YAAA,CAClD;UACL,CACG,CAAC,eAENnE,uDAAA;YAAG4B,SAAS,EAAC,uBAAuB;YAAAvB,QAAA,EAAEyP,SAAS,CAACC;UAAW,CAAI,CAAC,eAEhE7P,wDAAA;YAAK0B,SAAS,EAAC,gBAAgB;YAAAvB,QAAA,gBAC9BL,uDAAA;cAAAK,QAAA,EAAOyP,SAAS,CAACA;YAAS,CAAO,CAAC,eAClC9P,uDAAA,CAACyN,yDAAM;cACNc,OAAO,EAAC,WAAW;cACnBvN,IAAI,EAAC,OAAO;cACZmF,OAAO,EAAEA,CAAA,KAAMoK,eAAe,CAACT,SAAS,CAACA,SAAS,CAAE;cACpDlO,SAAS,EACR8N,eAAe,KAAKI,SAAS,CAACA,SAAS,GAAG,QAAQ,GAAG,EACrD;cAAAzP,QAAA,EAEAqP,eAAe,KAAKI,SAAS,CAACA,SAAS,GACrC3L,mDAAE,CAAC,SAAS,EAAE,sBAAsB,CAAC,GACrCA,mDAAE,CAAC,MAAM,EAAE,sBAAsB;YAAC,CAC9B,CAAC;UAAA,CACL,CAAC;QAAA,CAkBG,CAAC;MAAA,GAxDD2L,SAAS,CAACD,EAyDf,CACN;IAAC,CACE,CAAC,EAELO,kBAAkB,CAACtM,MAAM,KAAK,CAAC,iBAC/B9D,uDAAA;MAAK4B,SAAS,EAAC,YAAY;MAAAvB,QAAA,eAC1BL,uDAAA;QAAAK,QAAA,EACE8D,mDAAE,CACF,2CAA2C,EAC3C,sBACD;MAAC,CACC;IAAC,CACA,CACL;EAAA,CACG,CAAC;AAER,CAAC;AAED,iEAAetE,UAAU,E;;;;;;;;;;;;;;;;;;;;ACjRY;AACiB;AACI;;AAE1D;AACA;AACA;AACA;AAHA;AAIA,MAAMyP,kBAAkB,GAAGA,CAAC;EAAEjL;AAAQ,CAAC,KAAK;EAC3C,MAAMqN,YAAY,GAAGjI,+CAAI,EAAEoE,gBAAgB,IAAI,CAAC,CAAC;EACjD,MAAM8D,cAAc,GAAGtN,OAAO,IAAIqN,YAAY,CAAC7J,eAAe,IAAI,IAAI;EAEtE,oBACC3H,uDAAA;IAAK0B,SAAS,EAAC,yBAAyB;IAAAvB,QAAA,gBACvCH,uDAAA;MAAG0B,SAAS,EAAC,yBAAyB;MAACoC,IAAI,EAAC,GAAG;MAAA3D,QAAA,gBAC9CL,sDAAA,CAACO,iEAAW;QAACC,IAAI,EAAC;MAAO,CAAE,CAAC,eAC5BR,sDAAA;QAAM4B,SAAS,EAAC,YAAY;QAAAvB,QAAA,EAAE8D,mDAAE,CAAC,YAAY,EAAE,WAAW;MAAC,CAAO,CAAC;IAAA,CACjE,CAAC,eAEJjE,uDAAA;MAAK0B,SAAS,EAAC,0BAA0B;MAAAvB,QAAA,GACvC4M,gEAAiB,CAAC0E,cAAc,CAAC,EAAC,GAAC,EAACxN,mDAAE,CAAC,SAAS,EAAE,WAAW,CAAC;IAAA,CAC3D,CAAC;EAAA,CACF,CAAC;AAER,CAAC;AAED,iEAAemL,kBAAkB,E;;;;;;;;;;;;;;;;;;;;ACrBR;AAC6B;AACjB;;AAErC;AACA;AACA;AACA;AAHA;AAIA,MAAMC,iBAAiB,GAAGA,CAAC;EAAE/M,MAAM;EAAE+D,KAAK;EAAElC;AAAQ,CAAC,KAAK;EACzD,MAAMqN,YAAY,GAAGjI,+CAAI,EAAEoE,gBAAgB,IAAI,CAAC,CAAC;EACjD,MAAM+D,YAAY,GAAGF,YAAY,CAAC5J,UAAU,IAAI,GAAG;EACnD,MAAM6J,cAAc,GAAGtN,OAAO,IAAIqN,YAAY,CAAC7J,eAAe,IAAI,GAAG;EAErE,MAAMnE,aAAa,GAAGlB,MAAM,IAAIL,4DAAa,CAACyP,YAAY,CAAC;EAE3D,oBACC1R,uDAAA;IAAK0B,SAAS,EAAC,iDAAiD;IAAAvB,QAAA,gBAC/DH,uDAAA;MAAK0B,SAAS,EAAC,8BAA8B;MAAAvB,QAAA,gBAC5CL,sDAAA;QAAM4B,SAAS,EAAC,eAAe;QAAAvB,QAAA,EAAEqD;MAAa,CAAO,CAAC,eACtD1D,sDAAA;QAAK4B,SAAS,EAAC,uBAAuB;QAAAvB,QAAA,eACrCL,sDAAA;UACCU,GAAG,EAAE0B,8DAAe,CAACwP,YAAY,CAAE;UACnCjR,GAAG,EAAE,GAAG+C,aAAa,cAAe;UACpC9C,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO;QAAE,CACzB;MAAC,CACE,CAAC;IAAA,CACF,CAAC,eAENX,uDAAA;MAAK0B,SAAS,EAAC,+BAA+B;MAAAvB,QAAA,gBAC7CL,sDAAA;QAAM4B,SAAS,EAAC,cAAc;QAAAvB,QAAA,EAC5B4M,gEAAiB,CAAC0E,cAAc;MAAC,CAC7B,CAAC,eACP3R,sDAAA;QAAM4B,SAAS,EAAC,eAAe;QAAAvB,QAAA,EAAE8D,mDAAE,CAAC,YAAY,EAAE,WAAW;MAAC,CAAO,CAAC,eACtEnE,sDAAA,CAACO,iEAAW;QAACK,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO;MAAE,CAAE,CAAC,eACzCb,sDAAA;QAAM4B,SAAS,EAAC,YAAY;QAAAvB,QAAA,EAAE8D,mDAAE,CAAC,YAAY,EAAE,WAAW;MAAC,CAAO,CAAC;IAAA,CAC/D,CAAC;EAAA,CACF,CAAC;AAER,CAAC;AAED,iEAAeoL,iBAAiB,E;;;;;;;;;;;;;;;;;;;;AC7C0B;AACJ;AACjB;AACrC;AACA;AACA;AACA;AAHA;AAIA,MAAMF,uBAAuB,GAAGA,CAAC;EAAEnF;AAAM,CAAC,KAAK;EAC9C;EACA,MAAMwH,YAAY,GAAGjI,+CAAI,EAAEoE,gBAAgB,IAAI,CAAC,CAAC;EACjD,MAAMgE,YAAY,GAAG3H,KAAK,IAAIwH,YAAY,CAAC7J,eAAe,IAAI,GAAG;EAEjE,oBACC7H,sDAAA;IAAAK,QAAA,eACCH,uDAAA;MAAG0B,SAAS,EAAC,mDAAmD;MAACoC,IAAI,EAAC,GAAG;MAAA3D,QAAA,gBACxEL,sDAAA;QAAM4B,SAAS,EAAC,UAAU;QAAAvB,QAAA,EAAE8D,mDAAE,CAAC,SAAS,EAAE,WAAW;MAAC,CAAO,CAAC,eAC9DnE,sDAAA;QAAM4B,SAAS,EAAC,cAAc;QAAAvB,QAAA,EAAE4M,gEAAiB,CAAC4E,YAAY;MAAC,CAAO,CAAC,eACvE7R,sDAAA;QAAM4B,SAAS,EAAC,eAAe;QAAAvB,QAAA,EAAE8D,mDAAE,CAAC,YAAY,EAAE,WAAW;MAAC,CAAO,CAAC,eACtEnE,sDAAA,CAACO,iEAAW;QAACK,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO;MAAE,CAAE,CAAC,eACzCb,sDAAA;QAAM4B,SAAS,EAAC,YAAY;QAAAvB,QAAA,EAAE8D,mDAAE,CAAC,YAAY,EAAE,WAAW;MAAC,CAAO,CAAC;IAAA,CACjE;EAAC,CACA,CAAC;AAER,CAAC;AAED,iEAAekL,uBAAuB,E;;;;;;;;;;;;;;;;;;;;ACzBD;AACkC;AACjB;;AAEtD;AACA;AACA;AACA;AAHA;AAIA,MAAMD,gBAAgB,GAAGA,CAAC;EAAE5M,MAAM;EAAE+D;AAAM,CAAC,KAAK;EAC/C,MAAMmL,YAAY,GAAGjI,+CAAI,EAAEoE,gBAAgB,IAAI,CAAC,CAAC;EACjD,MAAM+D,YAAY,GAAGF,YAAY,CAAC5J,UAAU,IAAI,GAAG;EAEnD,MAAMpE,aAAa,GAAGlB,MAAM,IAAIL,4DAAa,CAACyP,YAAY,CAAC;EAE3D,oBACC1R,uDAAA;IAAK0B,SAAS,EAAC,kDAAkD;IAAAvB,QAAA,gBAChEL,sDAAA;MAAK4B,SAAS,EAAC,gBAAgB;MAAAvB,QAAA,EAAEqD;IAAa,CAAM,CAAC,eACrD1D,sDAAA;MAAK4B,SAAS,EAAC,eAAe;MAAAvB,QAAA,eAC7BL,sDAAA;QACCU,GAAG,EAAE0B,8DAAe,CAACwP,YAAY,CAAE;QACnCjR,GAAG,EAAE,GAAG+C,aAAa,cAAe;QACpC9C,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO;MAAE,CACzB;IAAC,CACE,CAAC,eACNX,uDAAA;MAAK0B,SAAS,EAAC,YAAY;MAAAvB,QAAA,gBAC1BL,sDAAA,CAACO,iEAAW,IAAE,CAAC,eACfP,sDAAA;QAAK4B,SAAS,EAAC,YAAY;QAAAvB,QAAA,EAAE8D,mDAAE,CAAC,YAAY,EAAE,WAAW;MAAC,CAAM,CAAC;IAAA,CAC7D,CAAC;EAAA,CACF,CAAC;;EAGP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACD,CAAC;AAED,iEAAeiL,gBAAgB,E;;;;;;;;;;;;;;;;;;;;ACzDL;AACa;AACe;;AAEtD;AACA;AACA;AACA;AAHA;AAIA,MAAMD,sBAAsB,GAAGA,CAAC;EAAE3M,MAAM;EAAE4K;AAAM,CAAC,KAAK;EACrD;EACA,MAAMsE,YAAY,GAAGjI,+CAAI,EAAEoE,gBAAgB,IAAI,CAAC,CAAC;EACjD,MAAM+D,YAAY,GAAGxE,KAAK,IAAIsE,YAAY,CAAC5J,UAAU,IAAI,GAAG;;EAE5D;EACA,MAAM3F,aAAa,GAAIiL,KAAK,IAAK;IAChC,IAAIA,KAAK,IAAI,GAAG,EAAE,OAAO,WAAW;IACpC,IAAIA,KAAK,IAAI,GAAG,EAAE,OAAO,OAAO;IAChC,IAAIA,KAAK,IAAI,GAAG,EAAE,OAAO,MAAM;IAC/B,IAAIA,KAAK,IAAI,GAAG,EAAE,OAAO,SAAS;IAClC,OAAO,MAAM;EACd,CAAC;EAED,MAAM1J,aAAa,GAAGlB,MAAM,IAAIL,aAAa,CAACyP,YAAY,CAAC;EAC3D,oBACC1R,uDAAA;IAAK0B,SAAS,EAAC,mDAAmD;IAAAvB,QAAA,gBAEjEH,uDAAA;MAAK0B,SAAS,EAAC,2BAA2B;MAAAvB,QAAA,gBACzCL,sDAAA;QAAM4B,SAAS,EAAC,eAAe;QAAAvB,QAAA,EAAEqD;MAAa,CAAO,CAAC,eAEtDxD,uDAAA;QAAK0B,SAAS,EAAC,iBAAiB;QAAAvB,QAAA,gBAC/BL,sDAAA;UAAM4B,SAAS,EAAC,0BAA0B;UAAAvB,QAAA,EAAEuR;QAAY,CAAO,CAAC,eAChE5R,sDAAA;UAAM4B,SAAS,EAAC,kBAAkB;UAAAvB,QAAA,EAAC;QAAS,CAAM,CAAC;MAAA,CAC/C,CAAC;IAAA,CACF,CAAC,eAGNH,uDAAA;MAAK0B,SAAS,EAAC,4BAA4B;MAAAvB,QAAA,gBAC1CL,sDAAA,CAACO,iEAAW,IAAE,CAAC,eACfP,sDAAA;QAAM4B,SAAS,EAAC,YAAY;QAAAvB,QAAA,EAAC;MAAU,CAAM,CAAC;IAAA,CAC1C,CAAC;EAAA,CACF,CAAC;AAER,CAAC;AAED,iEAAe8O,sBAAsB,E;;;;;;;;;;;;;;;;;;;;AC5CiB;AAM7B;AACY;;AAErC;AACA;AACA;AACA;AAHA;AAIA,MAAMF,mBAAmB,GAAGA,CAAC;EAAE6C,UAAU;EAAEzN;AAAQ,CAAC,KAAK;EACxD,MAAMqN,YAAY,GAAGjI,+CAAI,EAAEoE,gBAAgB,IAAI,CAAC,CAAC;EACjD,MAAMkE,iBAAiB,GAAGD,UAAU,IAAIJ,YAAY,CAAC5J,UAAU,IAAI,GAAG;EACtE,MAAM6J,cAAc,GAAGtN,OAAO,IAAIqN,YAAY,CAAC7J,eAAe,IAAI,GAAG;EAErE,oBACC3H,uDAAA;IAAK0B,SAAS,EAAC,6BAA6B;IAAAvB,QAAA,gBAC3CH,uDAAA;MAAK0B,SAAS,EAAC,0BAA0B;MAAAvB,QAAA,gBACxCL,sDAAA,CAACO,iEAAW,IAAE,CAAC,eACfP,sDAAA;QAAM4B,SAAS,EAAC,YAAY;QAAAvB,QAAA,EAAE8D,mDAAE,CAAC,YAAY,EAAE,WAAW;MAAC,CAAO,CAAC;IAAA,CAC/D,CAAC,eAENnE,sDAAA;MAAK4B,SAAS,EAAC,cAAc;MAAAvB,QAAA,eAC5BL,sDAAA;QACCU,GAAG,EAAE0B,8DAAe,CAAC2P,iBAAiB,CAAE;QACxCpR,GAAG,EAAE,GAAGoR,iBAAiB;MAAe,CACxC;IAAC,CACE,CAAC,eAGN7R,uDAAA;MAAK0B,SAAS,EAAC,6BAA6B;MAAAvB,QAAA,gBAC3CH,uDAAA;QAAK0B,SAAS,EAAC,wBAAwB;QAAAvB,QAAA,gBACtCH,uDAAA;UAAM0B,SAAS,EAAC,eAAe;UAAAvB,QAAA,GAC7B8D,mDAAE,CAAC,YAAY,EAAE,WAAW,CAAC,EAAE,GAAG;QAAA,CAC9B,CAAC,eACPnE,sDAAA;UAAM4B,SAAS,EAAC,0BAA0B;UAAAvB,QAAA,EAAE0R;QAAiB,CAAO,CAAC;MAAA,CACjE,CAAC,eAEN7R,uDAAA;QAAK0B,SAAS,EAAC,uBAAuB;QAAAvB,QAAA,gBACrCL,sDAAA;UAAM4B,SAAS,EAAC,kBAAkB;UAAAvB,QAAA,EAChC4M,gEAAiB,CAAC0E,cAAc;QAAC,CAC7B,CAAC,eACPzR,uDAAA;UAAM0B,SAAS,EAAC,mBAAmB;UAAAvB,QAAA,GACjC,GAAG,EACH8D,mDAAE,CAAC,SAAS,EAAE,WAAW,CAAC;QAAA,CACtB,CAAC;MAAA,CACH,CAAC;IAAA,CACF,CAAC;EAAA,CACF,CAAC;AAER,CAAC;AAED,iEAAe8K,mBAAmB,E;;;;;;;;;;;;;;;;;;;AClDT;AACY;AACrC;AACA;AACA;AACA;AAHA;AAIA,MAAMC,sBAAsB,GAAGA,CAAC;EAAE7K;AAAQ,CAAC,KAAK;EAC/C,MAAMqN,YAAY,GAAGjI,+CAAI,EAAEoE,gBAAgB,IAAI,CAAC,CAAC;EACjD,MAAM8D,cAAc,GAAGtN,OAAO,IAAIqN,YAAY,CAAC7J,eAAe,IAAI,IAAI;EACtE,MAAMkK,iBAAiB,GAAGL,YAAY,CAAC5J,UAAU,IAAI,GAAG;EAExD,oBACC5H,uDAAA;IAAK0B,SAAS,EAAC,gCAAgC;IAAAvB,QAAA,gBAC9CH,uDAAA;MAAK0B,SAAS,EAAC,0BAA0B;MAAAvB,QAAA,gBACxCH,uDAAA;QAAM0B,SAAS,EAAC,UAAU;QAAAvB,QAAA,GAAE8D,mDAAE,CAAC,eAAe,EAAE,WAAW,CAAC,EAAC,GAAC;MAAA,CAAM,CAAC,eACrEnE,sDAAA;QAAM4B,SAAS,EAAC,cAAc;QAAAvB,QAAA,EAC5B4M,gEAAiB,CAAC0E,cAAc;MAAC,CAC7B,CAAC,eACPzR,uDAAA;QAAM0B,SAAS,EAAC,eAAe;QAAAvB,QAAA,GAAC,GAAC,EAAC8D,mDAAE,CAAC,SAAS,EAAE,WAAW,CAAC;MAAA,CAAO,CAAC;IAAA,CAChE,CAAC,eAENnE,sDAAA;MAAK4B,SAAS,EAAC,cAAc;MAAAvB,QAAA,eAC5BL,sDAAA;QACCU,GAAG,EAAE0B,8DAAe,CAAC2P,iBAAiB,CAAE;QACxCpR,GAAG,EAAE,GAAGoR,iBAAiB;MAAe,CACxC;IAAC,CACE,CAAC,eAEN/R,sDAAA;MAAK4B,SAAS,EAAC,uBAAuB;MAAAvB,QAAA,eACrCH,uDAAA;QAAK0B,SAAS,EAAC,0BAA0B;QAAAvB,QAAA,gBACxCL,sDAAA,CAACgS,OAAO,IAAE,CAAC,eAEXhS,sDAAA;UACCU,GAAG,EAAEJ,0DAAe,GAAG,gCAAiC;UACxDK,GAAG,EAAC;QAAiB,CACrB,CAAC,eACFX,sDAAA;UAAM4B,SAAS,EAAC,gBAAgB;UAAAvB,QAAA,EAAC;QAAU,CAAM,CAAC;MAAA,CAC9C;IAAC,CACF,CAAC;EAAA,CACF,CAAC;AAER,CAAC;AAED,iEAAe6O,sBAAsB,EAAC;AAEtC,MAAM8C,OAAO,GAAGA,CAAA,kBACf9R,uDAAA;EAAK0B,SAAS,EAAC,2CAA2C;EAAAvB,QAAA,gBACzDL,sDAAA;IAAK4B,SAAS,EAAC,2BAA2B;IAAAvB,QAAA,EACxC8D,mDAAE,CAAC,wCAAwC,EAAE,WAAW;EAAC,CACtD,CAAC,eACNnE,sDAAA;IAAGgE,IAAI,EAAC,GAAG;IAACpC,SAAS,EAAC,wBAAwB;IAAAvB,QAAA,EAC5C8D,mDAAE,CAAC,wBAAwB,EAAE,WAAW;EAAC,CACxC,CAAC;AAAA,CACA,CACL,C;;;;;;;;;;AC3DD,4C;;;;;;;;;;ACAA,sC;;;;;;;;;;ACAA,iC;;;;;;;;;;ACAA,oC;;;;;;;;;;ACAA,2C;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA,E;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA,E;;;;;WCPA,wF;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D,E;;;;;;;;;;;;;;;;;;;;ACN0B;AACa;AACJ;AACF;AACjC;AACwE;AAAA;AAExE0M,QAAQ,CAACqB,gBAAgB,CAAC,kBAAkB,EAAE,YAAY;EACxD,MAAMpF,IAAI,GAAG+D,QAAQ,CAACsB,cAAc,CAAC,4BAA4B,CAAC;EAClE,MAAMC,IAAI,GAAGH,qDAAU,CAACnF,IAAI,CAAC;EAE7BsF,IAAI,CAACC,MAAM,cACTrS,sDAAA,CAAC2J,iFAAmB;IAAAtJ,QAAA,eAClBL,sDAAA,CAACG,uDAAG,IAAE;EAAC,CACY,CACvB,CAAC;AACH,CAAC,CAAC,C", "sources": ["webpack://trustpilot-reviewkit/./react_app/assets/scss/main.scss", "webpack://trustpilot-reviewkit/./react_app/components/App.jsx", "webpack://trustpilot-reviewkit/./react_app/components/containers/preview/image.jsx", "webpack://trustpilot-reviewkit/./react_app/components/containers/reviews/CustomerInfo.jsx", "webpack://trustpilot-reviewkit/./react_app/components/containers/reviews/ReviewCard.jsx", "webpack://trustpilot-reviewkit/./react_app/components/containers/reviews/ReviewList.jsx", "webpack://trustpilot-reviewkit/./react_app/components/containers/reviews/StarRating.jsx", "webpack://trustpilot-reviewkit/./react_app/components/containers/reviews/TrustpilotBusinessPage.jsx", "webpack://trustpilot-reviewkit/./react_app/components/context/data-context.jsx", "webpack://trustpilot-reviewkit/./react_app/components/helper/utils.js", "webpack://trustpilot-reviewkit/./react_app/components/pages/Layout.jsx", "webpack://trustpilot-reviewkit/./react_app/components/pages/reviews/index.jsx", "webpack://trustpilot-reviewkit/./react_app/components/pages/shortcodes/index.jsx", "webpack://trustpilot-reviewkit/./react_app/components/shortcode-previews/MicroButtonPreview.jsx", "webpack://trustpilot-reviewkit/./react_app/components/shortcode-previews/MicroComboPreview.jsx", "webpack://trustpilot-reviewkit/./react_app/components/shortcode-previews/MicroReviewCountPreview.jsx", "webpack://trustpilot-reviewkit/./react_app/components/shortcode-previews/MicroStarPreview.jsx", "webpack://trustpilot-reviewkit/./react_app/components/shortcode-previews/MicroTrustScorePreview.jsx", "webpack://trustpilot-reviewkit/./react_app/components/shortcode-previews/MiniTrustboxPreview.jsx", "webpack://trustpilot-reviewkit/./react_app/components/shortcode-previews/StarterTrustboxPreview.jsx", "webpack://trustpilot-reviewkit/external window [\"wp\",\"components\"]", "webpack://trustpilot-reviewkit/external window [\"wp\",\"i18n\"]", "webpack://trustpilot-reviewkit/external window \"React\"", "webpack://trustpilot-reviewkit/external window \"ReactDOM\"", "webpack://trustpilot-reviewkit/external window \"ReactJSXRuntime\"", "webpack://trustpilot-reviewkit/webpack/bootstrap", "webpack://trustpilot-reviewkit/webpack/runtime/compat get default export", "webpack://trustpilot-reviewkit/webpack/runtime/define property getters", "webpack://trustpilot-reviewkit/webpack/runtime/hasOwnProperty shorthand", "webpack://trustpilot-reviewkit/webpack/runtime/make namespace object", "webpack://trustpilot-reviewkit/./react_app/index.js"], "sourcesContent": ["// extracted by mini-css-extract-plugin\nexport {};", "import React from \"react\";\nimport Layout from \"./pages/Layout\";\nimport { useDataContext } from \"./context/data-context\";\nimport ShortCodes from \"./pages/shortcodes\";\nimport Reviews from \"./pages/reviews\";\n\nconst App = () => {\n  const { tab } = useDataContext();\n  return (\n    <div>\n      <Layout />\n      {tab === \"reviews\" && <Reviews />}\n      {tab === \"shortcodes\" && <ShortCodes />}\n    </div>\n  );\n};\n\nexport default App;\n", "import { plugin_root_url } from \"../../helper/utils\";\n\nconst SingleImage = ({type='', ...rest}) => {\n    if( type === 'light' ) {\n        return (\n            <img\n                src={plugin_root_url + \"/assets/images/light-single-star.png\"}\n                alt=\"Trustpilot Logo\"\n                style={{ width: '20px' }}\n            />\n        )\n    }\n\n    return ( \n        <img\n            src={plugin_root_url + \"/assets/images/single-star.svg\"}\n            alt=\"Trustpilot Logo\"\n            {...rest}\n        />\n     );\n}\n \nexport default SingleImage;", "import React from \"react\";\n\n/**\n * CustomerInfo component for displaying customer information\n * @param {Object} customer - Customer object with id, name, and image\n * @param {string} customer.name - Customer name\n * @param {string} customer.image - Customer profile image URL\n * @param {string} size - Size variant: 'small', 'medium', 'large' (default: 'medium')\n * @param {boolean} showImage - Whether to show customer image (default: true)\n */\nconst CustomerInfo = ({ customer = {}, size = \"medium\", showImage = true }) => {\n  const { name = \"Anonymous\", image = \"\" } = customer;\n\n  // Generate initials from name for fallback\n  const getInitials = (name) => {\n    if (!name || name === \"Customer\") return \"?\";\n    return name\n      .split(\" \")\n      .map((word) => word.charAt(0))\n      .join(\"\")\n      .toUpperCase()\n      .slice(0, 2);\n  };\n\n  return (\n    <div className={`customer-info customer-info--${size}`}>\n      {showImage && (\n        <div className=\"customer-avatar\">\n          {image ? (\n            <img\n              src={image}\n              alt={`${name}'s profile`}\n              className=\"customer-image\"\n              onError={(e) => {\n                e.target.style.display = \"none\";\n                e.target.nextSibling.style.display = \"flex\";\n              }}\n            />\n          ) : null}\n          <div\n            className=\"customer-initials\"\n            style={{ display: image ? \"none\" : \"flex\" }}\n          >\n            {getInitials(name)}\n          </div>\n        </div>\n      )}\n      <div className=\"customer-details\">\n        <span className=\"customer-name\">{name}</span>\n      </div>\n    </div>\n  );\n};\n\nexport default CustomerInfo;\n", "import React from \"react\";\nimport StarRating from \"./StarRating\";\nimport CustomerInfo from \"./CustomerInfo\";\nimport { getRatingText, getStarImageUrl } from \"../../helper/utils\";\n\n/**\n * ReviewCard component for displaying individual review\n * @param {Object} review - Review object\n * @param {string} review.reviewId - Review ID\n * @param {number} review.rating - Review rating (1-5)\n * @param {string} review.reviewTitle - Review title\n * @param {string} review.reviewBody - Review content\n * @param {Object} review.customer - Customer information\n * @param {Object} review.dates - Review dates\n * @param {string} review.reviewUrl - Review URL on Trustpilot\n */\nconst ReviewCard = ({ review = {} }) => {\n\tconst {\n\t\treviewId,\n\t\trating = 0,\n\t\treviewTitle = \"\",\n\t\treviewBody = \"\",\n\t\tcustomer = {},\n\t\tdates = {},\n\t\treviewUrl = \"\",\n\t} = review;\n\n\tconsole.info(review);\n\n\tconst { publishedDate, experiencedDate } = dates;\n\n\t// Format date for display\n\tconst formatDate = (dateString) => {\n\t\tif (!dateString) return \"\";\n\t\ttry {\n\t\t\tconst date = new Date(dateString);\n\t\t\treturn date.toLocaleDateString(\"en-US\", {\n\t\t\t\tyear: \"numeric\",\n\t\t\t\tmonth: \"short\",\n\t\t\t\tday: \"numeric\",\n\t\t\t});\n\t\t} catch (error) {\n\t\t\treturn \"\";\n\t\t}\n\t};\n\n\tconst dynamicRating = getRatingText(rating);\n\n\t// Truncate long review text\n\tconst truncateText = (text, maxLength = 500) => {\n\t\tif (!text || text.length <= maxLength) return text;\n\t\treturn text.substring(0, maxLength) + \"...\";\n\t};\n\n\treturn (\n\t\t<div className=\"review-card\" data-review-id={reviewId}>\n\t\t\t<div className=\"review-header\">\n\t\t\t\t<CustomerInfo customer={customer} size=\"small\" />\n\t\t\t\t<div className=\"review-meta\">\n\t\t\t\t\t<div className=\"dynamic_stars\" style={{ width: \"150px\" }}>\n\t\t\t\t\t\t<img\n\t\t\t\t\t\t\tsrc={getStarImageUrl(review.rating)}\n\t\t\t\t\t\t\talt={`${dynamicRating} star rating`}\n\t\t\t\t\t\t\tstyle={{ width: \"100%\" }}\n\t\t\t\t\t\t/>\n\t\t\t\t\t</div>\n\t\t\t\t\t{publishedDate && (\n\t\t\t\t\t\t<span className=\"review-date\">{formatDate(publishedDate)}</span>\n\t\t\t\t\t)}\n\t\t\t\t</div>\n\t\t\t</div>\n\n\t\t\t<div className=\"review-content\">\n\t\t\t\t{reviewTitle && <h4 className=\"review-title\">{reviewTitle}</h4>}\n\t\t\t\t{reviewBody && (\n\t\t\t\t\t<p className=\"review-body\">{truncateText(reviewBody)}</p>\n\t\t\t\t)}\n\t\t\t\t{experiencedDate && (\n\t\t\t\t\t<div className=\"review-experience-date\">\n\t\t\t\t\t\t<small>Experience date: {formatDate(experiencedDate)}</small>\n\t\t\t\t\t</div>\n\t\t\t\t)}\n\t\t\t</div>\n\n\t\t\t{reviewUrl && (\n\t\t\t\t<div className=\"review-footer\">\n\t\t\t\t\t<a\n\t\t\t\t\t\thref={reviewUrl}\n\t\t\t\t\t\ttarget=\"_blank\"\n\t\t\t\t\t\trel=\"noopener noreferrer\"\n\t\t\t\t\t\tclassName=\"review-link\"\n\t\t\t\t\t>\n\t\t\t\t\t\tView on Trustpilot\n\t\t\t\t\t</a>\n\t\t\t\t</div>\n\t\t\t)}\n\t\t</div>\n\t);\n};\n\nexport default ReviewCard;\n", "import React, { useState } from \"react\";\nimport ReviewCard from \"./ReviewCard\";\nimport { __ } from '@wordpress/i18n';\n\n/**\n * ReviewList component for displaying a list of reviews\n * @param {Object} reviews - Reviews object from API response (keyed by review ID)\n * @param {number} initialDisplayCount - Initial number of reviews to display (default: 10)\n * @param {boolean} showLoadMore - Whether to show load more button (default: true)\n */\nconst ReviewList = ({ \n  reviews = {}, \n  initialDisplayCount = 10, \n  showLoadMore = true \n}) => {\n  const [displayCount, setDisplayCount] = useState(initialDisplayCount);\n\n  // Convert reviews object to array and sort by published date (newest first)\n  const reviewsArray = Object.values(reviews).sort((a, b) => {\n    const dateA = new Date(a.dates?.publishedDate || 0);\n    const dateB = new Date(b.dates?.publishedDate || 0);\n    return dateB - dateA;\n  });\n\n  const totalReviews = reviewsArray.length;\n  const displayedReviews = reviewsArray.slice(0, displayCount);\n  const hasMoreReviews = displayCount < totalReviews;\n\n  // Load more reviews\n  const handleLoadMore = () => {\n    setDisplayCount(prev => Math.min(prev + initialDisplayCount, totalReviews));\n  };\n\n  // Reset to initial count\n  const handleShowLess = () => {\n    setDisplayCount(initialDisplayCount);\n  };\n\n  // Filter reviews by rating\n  const [ratingFilter, setRatingFilter] = useState(null);\n  \n  const filteredReviews = ratingFilter \n    ? displayedReviews.filter(review => review.rating === ratingFilter)\n    : displayedReviews;\n\n  // Get rating distribution\n  const getRatingDistribution = () => {\n    const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };\n    reviewsArray.forEach(review => {\n      if (review.rating >= 1 && review.rating <= 5) {\n        distribution[Math.floor(review.rating)]++;\n      }\n    });\n    return distribution;\n  };\n\n  const ratingDistribution = getRatingDistribution();\n\n  if (totalReviews === 0) {\n    return (\n      <div className=\"review-list\">\n        <div className=\"review-list-empty\">\n          <p>{__('>No reviews available', 'reviewkit')}</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"review-list\">\n      <div className=\"review-list-header\">\n        <h3 className=\"review-list-title\">\n          Customer Reviews ({totalReviews})\n        </h3>\n        \n        {/* Rating Filter */}\n        <div className=\"review-filters\">\n          <div className=\"rating-filter\">\n            <button\n              className={`filter-btn ${ratingFilter === null ? 'active' : ''}`}\n              onClick={() => setRatingFilter(null)}\n            >\n              All\n            </button>\n            {[5, 4, 3, 2, 1].map(rating => (\n              <button\n                key={rating}\n                className={`filter-btn ${ratingFilter === rating ? 'active' : ''}`}\n                onClick={() => setRatingFilter(rating)}\n              >\n                {rating}★ ({ratingDistribution[rating]})\n              </button>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      <div className=\"review-list-content\">\n        {filteredReviews.length === 0 ? (\n          <div className=\"review-list-empty\">\n            <p>No reviews found for the selected rating</p>\n          </div>\n        ) : (\n          <div className=\"review-cards\">\n            {filteredReviews.map((review) => (\n              <ReviewCard \n                key={review.reviewId} \n                review={review} \n              />\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Load More / Show Less Controls */}\n      {showLoadMore && ratingFilter === null && (\n        <div className=\"review-list-controls\">\n          {hasMoreReviews && (\n            <button \n              className=\"load-more-btn\"\n              onClick={handleLoadMore}\n            >\n              Load More Reviews ({totalReviews - displayCount} remaining)\n            </button>\n          )}\n          \n          {displayCount > initialDisplayCount && (\n            <button \n              className=\"show-less-btn\"\n              onClick={handleShowLess}\n            >\n              Show Less\n            </button>\n          )}\n        </div>\n      )}\n\n      {/* Review Summary */}\n      <div className=\"review-summary\">\n        <p className=\"review-count-summary\">\n          Showing {filteredReviews.length} of {totalReviews} reviews\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default ReviewList;\n", "import React from \"react\";\n\n/**\n * StarRating component for displaying star ratings with authentic Trustpilot design\n * @param {number} rating - The rating value (0-5)\n * @param {number} maxStars - Maximum number of stars (default: 5)\n * @param {string} size - Size of stars: 'small', 'medium', 'large' (default: 'medium')\n * @param {boolean} showRating - Whether to show the numeric rating (default: false)\n * @param {boolean} useSvg - Whether to use SVG stars instead of Unicode (default: false)\n */\nconst StarRating = ({ rating = 0, maxStars = 5, size = \"medium\", showRating = false, useSvg = false }) => {\n  const stars = [];\n  const fullStars = Math.floor(rating);\n  const hasHalfStar = rating % 1 !== 0;\n\n  // Get Trustpilot color based on rating\n  const getTrustpilotColor = (rating) => {\n    if (rating <= 1) return \"#ff3722\"; // Red\n    if (rating <= 2) return \"#ff8622\"; // Orange\n    if (rating <= 3) return \"#ffce00\"; // Yellow\n    if (rating <= 4) return \"#73cf11\"; // Light Green\n    return \"#00b67a\"; // Trustpilot Green\n  };\n\n  const starColor = getTrustpilotColor(rating);\n\n  // SVG Star component for authentic Trustpilot look\n  const TrustpilotSvgStar = ({ filled, color }) => (\n    <svg\n      className={`star-svg ${filled ? 'star-filled' : 'star-empty'}`}\n      viewBox=\"0 0 24 24\"\n      style={{ fill: filled ? color : '#ddd' }}\n    >\n      <path d=\"M12 2l2.938 7.968h8.382l-6.76 4.898 2.553 7.834L12 17.334l-7.113 5.366 2.553-7.834-6.76-4.898h8.382z\" />\n    </svg>\n  );\n\n  // Generate full stars\n  for (let i = 0; i < fullStars; i++) {\n    stars.push(\n      useSvg ? (\n        <TrustpilotSvgStar\n          key={`full-${i}`}\n          filled={true}\n          color={starColor}\n        />\n      ) : (\n        <span\n          key={`full-${i}`}\n          className=\"star star-full\"\n          style={{ color: starColor }}\n        >\n          ★\n        </span>\n      )\n    );\n  }\n\n  // Add half star if needed\n  if (hasHalfStar && fullStars < maxStars) {\n    stars.push(\n      useSvg ? (\n        <div key=\"half\" className=\"star-svg-half\" style={{ position: 'relative' }}>\n          <TrustpilotSvgStar filled={false} color=\"#ddd\" />\n          <div style={{\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            width: '50%',\n            overflow: 'hidden'\n          }}>\n            <TrustpilotSvgStar filled={true} color={starColor} />\n          </div>\n        </div>\n      ) : (\n        <span\n          key=\"half\"\n          className=\"star star-half\"\n          style={{ color: starColor }}\n        >\n          ★\n        </span>\n      )\n    );\n  }\n\n  // Generate empty stars\n  const emptyStars = maxStars - fullStars - (hasHalfStar ? 1 : 0);\n  for (let i = 0; i < emptyStars; i++) {\n    stars.push(\n      useSvg ? (\n        <TrustpilotSvgStar\n          key={`empty-${i}`}\n          filled={false}\n          color=\"#ddd\"\n        />\n      ) : (\n        <span key={`empty-${i}`} className=\"star star-empty\">\n          ★\n        </span>\n      )\n    );\n  }\n\n  return (\n    <div className={`trustpilot-star-rating trustpilot-star-rating--${size}`}>\n      <div className=\"stars\">{stars}</div>\n      {showRating && <span className=\"rating-value\">({rating})</span>}\n    </div>\n  );\n};\n\nexport default StarRating;\n", "import { getRatingText, getStarImageUrl } from \"../../helper/utils\";\nimport { __ } from \"@wordpress/i18n\";\n\n/**\n * TrustpilotBusinessPage component for displaying business information\n * @param {Object} businessDetails - Business details object from API response\n */\nconst TrustpilotBusinessPage = ({ businessDetails = {} }) => {\n\tconst {\n\t\tdisplayName = \"\",\n\t\tidentifyingName = \"\",\n\t\tnumberOfReviews = 0,\n\t\ttrustScore = 0,\n\t\twebsiteUrl = \"\",\n\t\tprofileImageUrl = \"\",\n\t\tstars = 0,\n\t\tcategories = [],\n\t\tactivity = {},\n\t} = businessDetails;\n\n\t// Format large numbers\n\tconst formatNumber = (num) => {\n\t\tif (num >= 1000000) {\n\t\t\treturn (num / 1000000).toFixed(1) + \"M\";\n\t\t} else if (num >= 1000) {\n\t\t\treturn (num / 1000).toFixed(1) + \"K\";\n\t\t}\n\t\treturn num.toString();\n\t};\n\n\tconst primaryCategory = categories.find((cat) => cat.isPrimary)?.name || \"\";\n\n\tconst isVerified = activity?.verification?.verifiedUserIdentity || false;\n\tconst isClaimed = activity?.isClaimed || false;\n\n\tconst dynamicRating = getRatingText(trustScore);\n\n\t// Format profile image URL\n\tconst getImageUrl = (url) => {\n\t\tif (!url) return \"\";\n\t\tif (url.startsWith(\"//\")) {\n\t\t\treturn `https:${url}`;\n\t\t}\n\t\treturn url;\n\t};\n\n\tif (!displayName) {\n\t\treturn (\n\t\t\t<div className=\"trustpilot-business-page\">\n\t\t\t\t<div className=\"business-placeholder\">\n\t\t\t\t\t<p>{__(\"No business details found\", \"reviewkit\")}</p>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t);\n\t}\n\n\tconsole.info(activity.replyBehavior);\n\treturn (\n\t\t<div className=\"trustpilot-business-page\">\n\t\t\t<div className=\"business-header\">\n\t\t\t\t<div className=\"business-logo\">\n\t\t\t\t\t{profileImageUrl ? (\n\t\t\t\t\t\t<img\n\t\t\t\t\t\t\tsrc={getImageUrl(profileImageUrl)}\n\t\t\t\t\t\t\talt={`${displayName} logo`}\n\t\t\t\t\t\t\tclassName=\"business-image\"\n\t\t\t\t\t\t\tonError={(e) => {\n\t\t\t\t\t\t\t\te.target.style.display = \"none\";\n\t\t\t\t\t\t\t\te.target.nextSibling.style.display = \"flex\";\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t/>\n\t\t\t\t\t) : null}\n\t\t\t\t\t<div\n\t\t\t\t\t\tclassName=\"business-initials\"\n\t\t\t\t\t\tstyle={{ display: profileImageUrl ? \"none\" : \"flex\" }}\n\t\t\t\t\t>\n\t\t\t\t\t\t{displayName.charAt(0).toUpperCase()}\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\n\t\t\t\t<div className=\"business-info\">\n\t\t\t\t\t<div className=\"business-name-section\">\n\t\t\t\t\t\t<h2 className=\"business-name\">{displayName}</h2>\n\t\t\t\t\t\t{isClaimed && (\n\t\t\t\t\t\t\t<span className=\"business-claimed\">\n\t\t\t\t\t\t\t\t✓ {__(\"Claimed\", \"reviewkit\")}\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t)}\n\t\t\t\t\t\t{isVerified && (\n\t\t\t\t\t\t\t<span className=\"business-verified\">\n\t\t\t\t\t\t\t\t✓ {__(\"Verified\", \"reviewkit\")}\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t)}\n\t\t\t\t\t</div>\n\n\t\t\t\t\t{identifyingName && (\n\t\t\t\t\t\t<p className=\"business-website\">{identifyingName}</p>\n\t\t\t\t\t)}\n\n\t\t\t\t\t{primaryCategory && (\n\t\t\t\t\t\t<p className=\"business-category\">{primaryCategory}</p>\n\t\t\t\t\t)}\n\n\t\t\t\t\t<div className=\"business-rating\">\n\t\t\t\t\t\t<div className=\"rating-percentage\">\n\t\t\t\t\t\t\t<div className=\"dynamic_stars\" style={{ width: \"200px\" }}>\n\t\t\t\t\t\t\t\t<img\n\t\t\t\t\t\t\t\t\tsrc={getStarImageUrl(trustScore)}\n\t\t\t\t\t\t\t\t\talt={`${dynamicRating} star rating`}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<span>{trustScore}</span>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<span className=\"review-count\">\n\t\t\t\t\t\t\t{__(\"Based on\", \"reviewkit\")} {formatNumber(numberOfReviews)}{\" \"}\n\t\t\t\t\t\t\t{__(\"reviews\", \"reviewkit\")}\n\t\t\t\t\t\t</span>\n\t\t\t\t\t</div>\n\n\t\t\t\t\t{websiteUrl && (\n\t\t\t\t\t\t<div className=\"business-links\">\n\t\t\t\t\t\t\t<a\n\t\t\t\t\t\t\t\thref={websiteUrl}\n\t\t\t\t\t\t\t\ttarget=\"_blank\"\n\t\t\t\t\t\t\t\trel=\"noopener noreferrer\"\n\t\t\t\t\t\t\t\tclassName=\"components-button is-secondary business-website-link\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{__(\"Visit Website\", \"reviewkit\")}\n\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t)}\n\t\t\t\t</div>\n\t\t\t</div>\n\n\t\t\t{activity?.replyBehavior && (\n\t\t\t\t<div className=\"business-stats\">\n\t\t\t\t\t<div className=\"stat-item\">\n\t\t\t\t\t\t<span className=\"stat-label\">{__(\"Reply Rate\", \"reviewkit\")}:</span>\n\t\t\t\t\t\t<span className=\"stat-value\">\n\t\t\t\t\t\t\t{activity?.replyBehavior?.replyPercentage?.toFixed(2)}%\n\t\t\t\t\t\t</span>\n\t\t\t\t\t</div>\n\t\t\t\t\t{activity.replyBehavior?.averageDaysToReply >= 0 && (\n\t\t\t\t\t\t<div className=\"stat-item\">\n\t\t\t\t\t\t\t<span className=\"stat-label\">\n\t\t\t\t\t\t\t\t{__(\"Avg. Reply Time\", \"reviewkit\")}:\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t<span className=\"stat-value\">\n\t\t\t\t\t\t\t\t{Math.round(activity.replyBehavior.averageDaysToReply)}{\" \"}\n\t\t\t\t\t\t\t\t{__(\"days\", \"reviewkit\")}\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t)}\n\t\t\t\t</div>\n\t\t\t)}\n\t\t</div>\n\t);\n};\n\nexport default TrustpilotBusinessPage;\n", "import React, { createContext, useContext, useState } from \"react\";\nconst DataContext = createContext();\nimport { data as reviewDetails } from \"../helper/utils\";\n\nfunction DataContextProvider({ children }) {\n  const [tab, setTab] = useState(\"reviews\");\n  const [data, setData] = useState(reviewDetails || {});\n\n  const value = {\n    tab,\n    setTab,\n    data,\n    setData,\n  };\n\n  return <DataContext.Provider value={value}>{children}</DataContext.Provider>;\n}\n\nfunction useDataContext() {\n  return useContext(DataContext);\n}\n\nexport { DataContextProvider, useDataContext };\n", "export const {\n  ajaxurl,\n  nonce,\n  plugin_root_url,\n  data,\n  count,\n  last_updated,\n  domain,\n  original_domain,\n} = window.trustpilot_reviewkit;\n/**\n * Utility functions for the Trustpilot ReviewKit\n */\n\n/**\n * Fetches Trustpilot reviews for a specified domain using direct API call\n *\n * @param {string} domain - The domain to fetch reviews for (e.g., \"divinext.com\")\n * @param {Object} options - Optional configuration options\n * @param {number} options.timeout - Request timeout in milliseconds (default: 10000)\n * @param {AbortSignal} options.signal - AbortController signal for cancelling the request\n * @returns {Promise<Object>} - Promise that resolves to the reviews data\n * @throws {Error} - Throws an error if the request fails\n */\nexport const getReviewsFromAPI = async (domain, options = {}) => {\n  if (!domain) {\n    throw new Error(\"Domain is required\");\n  }\n\n  // Remove any protocol and trailing slashes to ensure consistent format\n  const cleanDomain = domain\n    .replace(/^(https?:\\/\\/)?(www\\.)?/, \"\")\n    .replace(/\\/$/, \"\");\n\n  const { timeout = 10000, signal } = options;\n\n  try {\n    // Create a controller for timeout if not provided externally\n    const controller = signal ? null : new AbortController();\n    const timeoutId = signal\n      ? null\n      : setTimeout(() => controller.abort(), timeout);\n\n    const response = await fetch(\n      `https://api.gutensuite.net/data/trustpilot/v1/reviews/${encodeURIComponent(\n        cleanDomain,\n      )}`,\n      {\n        method: \"GET\",\n        headers: {\n          Accept: \"application/json\",\n          \"Content-Type\": \"application/json\",\n        },\n        signal: signal || controller.signal,\n      },\n    );\n\n    // Clear timeout if we created one\n    if (timeoutId) {\n      clearTimeout(timeoutId);\n    }\n\n    if (!response.ok) {\n      throw new Error(\n        `Failed to fetch reviews: ${response.status} ${response.statusText}`,\n      );\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    if (error.name === \"AbortError\") {\n      throw new Error(\"Request timed out\");\n    }\n    throw error;\n  }\n};\n\n/**\n * Fetches Trustpilot reviews for a specified domain using WordPress AJAX\n *\n * @param {string} domain - The domain to fetch reviews for (e.g., \"divinext.com\")\n * @param {Object} options - Optional configuration options\n * @param {number} options.timeout - Request timeout in milliseconds (default: 30000)\n * @returns {Promise<Object>} - Promise that resolves to the reviews data\n * @throws {Error} - Throws an error if the request fails\n */\nexport const getReviews = (domain, options = {}) => {\n  if (!domain) {\n    return Promise.reject(new Error(\"Domain is required\"));\n  }\n\n  const { timeout = 30000 } = options;\n\n  // Ensure we have access to WordPress ajax functionality\n  if (\n    typeof window.ajaxurl === \"undefined\" ||\n    typeof window.trustpilot_reviewkit === \"undefined\"\n  ) {\n    return Promise.reject(\n      new Error(\n        \"WordPress AJAX not available. Make sure the script is properly enqueued.\",\n      ),\n    );\n  }\n\n  return new Promise((resolve, reject) => {\n    // Create a FormData object for the request\n    const formData = new FormData();\n    formData.append(\"action\", \"trustpilot_reviewkit_get_reviews\");\n    formData.append(\"security\", nonce);\n    formData.append(\"domain\", domain);\n    formData.append(\"revalidate\", options.revalidate);\n\n    // Use jQuery if available, otherwise use fetch\n    if (window.jQuery) {\n      jQuery.ajax({\n        url: window.ajaxurl,\n        type: \"POST\",\n        data: formData,\n        processData: false,\n        contentType: false,\n        timeout: timeout,\n        success: function (response) {\n          if (response.success) {\n            resolve(response.data);\n          } else {\n            reject(\n              new Error(response.data?.message || \"Unknown error occurred\"),\n            );\n          }\n        },\n        error: function (xhr, status, error) {\n          if (status === \"timeout\") {\n            reject(new Error(\"Request timed out\"));\n          } else {\n            reject(new Error(`AJAX request failed: ${error}`));\n          }\n        },\n      });\n    } else {\n      // Fallback to using fetch API if jQuery is not available\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), timeout);\n\n      fetch(window.ajaxurl, {\n        method: \"POST\",\n        body: formData,\n        signal: controller.signal,\n      })\n        .then((response) => {\n          clearTimeout(timeoutId);\n          if (!response.ok) {\n            throw new Error(\n              `Server returned ${response.status}: ${response.statusText}`,\n            );\n          }\n          return response.json();\n        })\n        .then((response) => {\n          if (response.success) {\n            resolve(response.data);\n          } else {\n            reject(\n              new Error(response.data?.message || \"Unknown error occurred\"),\n            );\n          }\n        })\n        .catch((error) => {\n          clearTimeout(timeoutId);\n          if (error.name === \"AbortError\") {\n            reject(new Error(\"Request timed out\"));\n          } else {\n            reject(error);\n          }\n        });\n    }\n  });\n};\n\n// Format review count\nexport const formatReviewCount = (count) => {\n  if (count >= 1000000) {\n    return (count / 1000000).toFixed(1) + \"M\";\n  } else if (count >= 1000) {\n    return (count / 1000).toFixed(1) + \"K\";\n  }\n  return count.toString();\n};\n\nexport const getStarImageUrl = (rating) => {\n\tconst roundedRating = Math.round(rating * 2) / 2; // Round to nearest 0.5\n\t// const filename = `stars-${roundedRating.toFixed(1).replace(\".\", \"_\")}.svg`;\n\tconst filename = `stars-${roundedRating.toFixed(1)}.svg`;\n\treturn plugin_root_url + \"/assets/images/\" + filename;\n};\n\n// Get rating text based on score\nexport const getRatingText = (score) => {\n  if (score >= 4.5) return \"Excellent\";\n  if (score >= 4.0) return \"Great\";\n  if (score >= 3.0) return \"Good\";\n  if (score >= 2.0) return \"Average\";\n  return \"Poor\";\n};", "import React from \"react\";\nimport { useDataContext } from \"../context/data-context\";\nconst Layout = () => {\n  const { tab, setTab } = useDataContext();\n\n  return (\n    <nav>\n      <ul>\n        {menuItems.map((item) => (\n          <li\n            key={item.label}\n            className={`${tab === item.tab ? \"active\" : \"\"}`}\n            onClick={() => setTab(item.tab)}\n          >\n            {item.label}\n          </li>\n        ))}\n      </ul>\n    </nav>\n  );\n};\n\nexport default Layout;\n\nconst menuItems = [\n  { label: \"Reviews\", tab: \"reviews\" },\n  { label: \"Shortcodes\", tab: \"shortcodes\" },\n];\n", "import React, { useState } from \"react\";\nimport { <PERSON>Control, <PERSON><PERSON>, Spinner } from \"@wordpress/components\";\nimport { __ } from \"@wordpress/i18n\";\nimport { getReviews, original_domain } from \"../../helper/utils\";\nimport { useDataContext } from \"../../context/data-context\";\nimport TrustpilotBusinessPage from \"../../containers/reviews/TrustpilotBusinessPage\";\nimport ReviewList from \"../../containers/reviews/ReviewList\";\n\nconst Reviews = () => {\n\tconst { data = {}, setData } = useDataContext();\n\tconst { business_details = {}, reviews = [] } = data;\n\tconst [trustpilotUrl, setTrustpilotUrl] = useState(original_domain);\n\tconst [loading, setLoading] = useState(false);\n\n\tconst __fetchReviews = async (revalidate = false) => {\n\t\tsetLoading(true);\n\t\tconst response = await getReviews(trustpilotUrl, { revalidate });\n\t\tif (response?.data) {\n\t\t\tsetData(response.data);\n\t\t}\n\t\tsetLoading(false);\n\t};\n\n\treturn (\n\t\t<div className=\"page review-page\">\n\t\t\t<div className=\"review-fetch\">\n\t\t\t\t<TextControl\n\t\t\t\t\t__nextHasNoMarginBottom\n\t\t\t\t\t__next40pxDefaultSize\n\t\t\t\t\tlabel={__(\"Trustpilot URL\", \"trustpilot-reviewkit\")}\n\t\t\t\t\ttype=\"url\"\n\t\t\t\t\thelp={__(\n\t\t\t\t\t\t\"Enter the URL of your Trustpilot page\",\n\t\t\t\t\t\t\"trustpilot-reviewkit\"\n\t\t\t\t\t)}\n\t\t\t\t\tvalue={trustpilotUrl}\n\t\t\t\t\tonChange={(value) => setTrustpilotUrl(value)}\n\t\t\t\t/>\n\t\t\t\t<div className=\"button-container\">\n\t\t\t\t\t<Button\n\t\t\t\t\t\tvariant=\"primary\"\n\t\t\t\t\t\tstyle={{ backgroundColor: \"#28a745\", fontWeight: \"600\" }}\n\t\t\t\t\t\tonClick={__fetchReviews}\n\t\t\t\t\t\tdisabled={trustpilotUrl === \"\"}\n\t\t\t\t\t>\n\t\t\t\t\t\t{__(\"Fetch Reviews\", \"trustpilot-reviewkit\")}\n\t\t\t\t\t</Button>\n\t\t\t\t\t<Button\n\t\t\t\t\t\tvariant=\"secondary\"\n\t\t\t\t\t\tonClick={() => __fetchReviews(true)}\n\t\t\t\t\t\tdisabled={!data}\n\t\t\t\t\t>\n\t\t\t\t\t\t{__(\"Revalidate Reviews\", \"trustpilot-reviewkit\")}\n\t\t\t\t\t</Button>\n\t\t\t\t</div>\n\t\t\t</div>\n\n\t\t\t{loading ? (\n\t\t\t\t<div style={{ marginTop: \"20px\", textAlign: \"center\" }}>\n\t\t\t\t\t<Spinner\n\t\t\t\t\t\tstyle={{\n\t\t\t\t\t\t\theight: \"calc(4px * 10)\",\n\t\t\t\t\t\t\twidth: \"calc(4px * 10)\",\n\t\t\t\t\t\t}}\n\t\t\t\t\t/>\n\t\t\t\t</div>\n\t\t\t) : (\n\t\t\t\t<>\n\t\t\t\t\t<TrustpilotBusinessPage businessDetails={business_details} />\n\t\t\t\t\t<ReviewList reviews={reviews} />\n\t\t\t\t</>\n\t\t\t)}\n\t\t</div>\n\t);\n};\n\nexport default Reviews;\n", "import React, { useState } from \"react\";\nimport {\n\tButton,\n\tTextControl,\n\tCard,\n\tCardBody,\n\tCardHeader,\n} from \"@wordpress/components\";\nimport { __ } from \"@wordpress/i18n\";\n\n// Import preview components\nimport MiniTrustboxPreview from \"../../shortcode-previews/MiniTrustboxPreview\";\nimport StarterTrustboxPreview from \"../../shortcode-previews/StarterTrustboxPreview\";\nimport MicroTrustScorePreview from \"../../shortcode-previews/MicroTrustScorePreview\";\nimport MicroStarPreview from \"../../shortcode-previews/MicroStarPreview\";\nimport MicroReviewCountPreview from \"../../shortcode-previews/MicroReviewCountPreview\";\nimport MicroButtonPreview from \"../../shortcode-previews/MicroButtonPreview\";\nimport MicroComboPreview from \"../../shortcode-previews/MicroComboPreview\";\nimport { plugin_root_url } from \"../../helper/utils\";\n\nconst ShortCodes = () => {\n\tconst [searchTerm, setSearchTerm] = useState(\"\");\n\tconst [copiedShortcode, setCopiedShortcode] = useState(\"\");\n\n\t// Shortcode data\n\tconst shortcodes = [\n\t\t{\n\t\t\tid: \"mini\",\n\t\t\tname: \"Mini Trustbox\",\n\t\t\tshortcode: \"[reviewkit_trustpilot_mini]\",\n\t\t\tdescription:\n\t\t\t\t\"Compact widget showing TrustScore, star rating, and review count\",\n\t\t\tcategory: \"free\",\n\t\t\tpreview: MiniTrustboxPreview,\n\t\t\tattributes: [\n\t\t\t\t{\n\t\t\t\t\tname: \"trustscore\",\n\t\t\t\t\tdefault: \"4.8\",\n\t\t\t\t\tdescription: \"Trust score rating (0-5)\",\n\t\t\t\t},\n\t\t\t\t{ name: \"reviews\", default: \"347\", description: \"Number of reviews\" },\n\t\t\t\t{ name: \"url\", default: \"#\", description: \"Link URL\" },\n\t\t\t],\n\t\t},\n\t\t{\n\t\t\tid: \"starter\",\n\t\t\tname: \"Starter Trustbox\",\n\t\t\tshortcode: \"[reviewkit_trustpilot_starter]\",\n\t\t\tdescription: \"Interactive widget with star rating and tooltip\",\n\t\t\tcategory: \"free\",\n\t\t\tpreview: StarterTrustboxPreview,\n\t\t\tattributes: [\n\t\t\t\t{ name: \"reviews\", default: \"1,376\", description: \"Number of reviews\" },\n\t\t\t\t{ name: \"url\", default: \"#\", description: \"Link URL\" },\n\t\t\t],\n\t\t},\n\t\t{\n\t\t\tid: \"micro_trustscore\",\n\t\t\tname: \"Micro TrustScore\",\n\t\t\tshortcode: \"[reviewkit_trustpilot_micro_trustscore]\",\n\t\t\tdescription: \"Simple display of TrustScore rating\",\n\t\t\tcategory: \"free\",\n\t\t\tpreview: MicroTrustScorePreview,\n\t\t\tattributes: [\n\t\t\t\t{ name: \"rating\", default: \"Excellent\", description: \"Rating text\" },\n\t\t\t\t{ name: \"score\", default: \"4.8\", description: \"Numeric score\" },\n\t\t\t\t{ name: \"url\", default: \"#\", description: \"Link URL\" },\n\t\t\t],\n\t\t},\n\t\t{\n\t\t\tid: \"micro_star\",\n\t\t\tname: \"Micro Star\",\n\t\t\tshortcode: \"[reviewkit_trustpilot_micro_star]\",\n\t\t\tdescription: \"Interactive star rating display\",\n\t\t\tcategory: \"free\",\n\t\t\tpreview: MicroStarPreview,\n\t\t\tattributes: [\n\t\t\t\t{ name: \"rating\", default: \"Excellent\", description: \"Rating text\" },\n\t\t\t\t{ name: \"stars\", default: \"5\", description: \"Number of stars (1-5)\" },\n\t\t\t\t{ name: \"url\", default: \"#\", description: \"Link URL\" },\n\t\t\t],\n\t\t},\n\t\t{\n\t\t\tid: \"micro_reviewcount\",\n\t\t\tname: \"Micro Review Count\",\n\t\t\tshortcode: \"[reviewkit_trustpilot_micro_reviewcount]\",\n\t\t\tdescription: \"Link showing review count\",\n\t\t\tcategory: \"free\",\n\t\t\tpreview: MicroReviewCountPreview,\n\t\t\tattributes: [\n\t\t\t\t{ name: \"count\", default: \"437\", description: \"Number of reviews\" },\n\t\t\t\t{ name: \"url\", default: \"#\", description: \"Link URL\" },\n\t\t\t],\n\t\t},\n\t\t{\n\t\t\tid: \"micro_button\",\n\t\t\tname: \"Micro Button\",\n\t\t\tshortcode: \"[reviewkit_trustpilot_micro_button]\",\n\t\t\tdescription: \"Button-style widget with review count\",\n\t\t\tcategory: \"free\",\n\t\t\tpreview: MicroButtonPreview,\n\t\t\tattributes: [\n\t\t\t\t{\n\t\t\t\t\tname: \"reviews\",\n\t\t\t\t\tdefault: \"1.3K+\",\n\t\t\t\t\tdescription: \"Review count display\",\n\t\t\t\t},\n\t\t\t\t{ name: \"url\", default: \"#\", description: \"Link URL\" },\n\t\t\t],\n\t\t},\n\t\t{\n\t\t\tid: \"micro_combo\",\n\t\t\tname: \"Micro Combo\",\n\t\t\tshortcode: \"[reviewkit_trustpilot_micro_combo]\",\n\t\t\tdescription: \"Combined star rating and review count widget\",\n\t\t\tcategory: \"free\",\n\t\t\tpreview: MicroComboPreview,\n\t\t\tattributes: [\n\t\t\t\t{ name: \"rating\", default: \"Excellent\", description: \"Rating text\" },\n\t\t\t\t{ name: \"stars\", default: \"5\", description: \"Number of stars (1-5)\" },\n\t\t\t\t{ name: \"reviews\", default: \"437\", description: \"Number of reviews\" },\n\t\t\t\t{ name: \"url\", default: \"#\", description: \"Link URL\" },\n\t\t\t],\n\t\t},\n\t];\n\n\t// Filter shortcodes based on search term\n\tconst filteredShortcodes = shortcodes.filter(\n\t\t(shortcode) =>\n\t\t\tshortcode.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n\t\t\tshortcode.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n\t\t\tshortcode.shortcode.toLowerCase().includes(searchTerm.toLowerCase())\n\t);\n\n\t// Copy shortcode to clipboard with fallback methods\n\tconst copyToClipboard = async (shortcode) => {\n\t\ttry {\n\t\t\t// Method 1: Modern Clipboard API\n\t\t\tif (navigator.clipboard && window.isSecureContext) {\n\t\t\t\tawait navigator.clipboard.writeText(shortcode);\n\t\t\t\tsetCopiedShortcode(shortcode);\n\t\t\t\tsetTimeout(() => setCopiedShortcode(\"\"), 2000);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Method 2: Fallback using document.execCommand (deprecated but widely supported)\n\t\t\tconst textArea = document.createElement(\"textarea\");\n\t\t\ttextArea.value = shortcode;\n\t\t\ttextArea.style.position = \"fixed\";\n\t\t\ttextArea.style.left = \"-999999px\";\n\t\t\ttextArea.style.top = \"-999999px\";\n\t\t\tdocument.body.appendChild(textArea);\n\t\t\ttextArea.focus();\n\t\t\ttextArea.select();\n\n\t\t\tconst successful = document.execCommand(\"copy\");\n\t\t\tdocument.body.removeChild(textArea);\n\n\t\t\tif (successful) {\n\t\t\t\tsetCopiedShortcode(shortcode);\n\t\t\t\tsetTimeout(() => setCopiedShortcode(\"\"), 2000);\n\t\t\t} else {\n\t\t\t\tthrow new Error(\"execCommand failed\");\n\t\t\t}\n\t\t} catch (err) {\n\t\t\tconsole.error(\"Failed to copy shortcode: \", err);\n\t\t\t// Method 3: Final fallback - show alert with shortcode\n\t\t\talert(`Copy this shortcode manually: ${shortcode}`);\n\t\t}\n\t};\n\n\treturn (\n\t\t<div className=\"page shortcodes-page\">\n\t\t\t<div className=\"shortcodes-header\" style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n\t\t\t\t<div>\n\t\t\t\t\t<h2>{__(\"Trustpilot Shortcodes\", \"trustpilot-reviewkit\")}</h2>\n\t\t\t\t\t<p>\n\t\t\t\t\t\t{__(\n\t\t\t\t\t\t\t\"Use these shortcodes to display Trustpilot widgets on your website.\",\n\t\t\t\t\t\t\t\"trustpilot-reviewkit\"\n\t\t\t\t\t\t)}\n\t\t\t\t\t</p>\n\t\t\t\t</div>\n\n\t\t\t\t<TextControl\n\t\t\t\t\t// label={__(\"Search Shortcodes\", \"trustpilot-reviewkit\")}\n\t\t\t\t\tvalue={searchTerm}\n\t\t\t\t\tonChange={setSearchTerm}\n\t\t\t\t\tplaceholder={__(\n\t\t\t\t\t\t\"Search by name, description, or shortcode...\",\n\t\t\t\t\t\t\"trustpilot-reviewkit\"\n\t\t\t\t\t)}\n\t\t\t\t\tclassName=\"shortcodes-search\"\n\t\t\t\t/>\n\t\t\t</div>\n\n\t\t\t<div className=\"shortcodes-grid\">\n\t\t\t\t{filteredShortcodes.map((shortcode) => (\n\t\t\t\t\t<Card key={shortcode.id} className={`shortcode-card ${shortcode.id}`}>\n\t\t\t\t\t\t<CardHeader>\n\t\t\t\t\t\t\t<h3>{shortcode.name}</h3>\n\t\t\t\t\t\t\t<span className=\"shortcode-category\">{shortcode.category}</span>\n\t\t\t\t\t\t</CardHeader>\n\t\t\t\t\t\t<CardBody>\n\t\t\t\t\t\t\t<div className=\"shortcode-preview\">\n\t\t\t\t\t\t\t\t{shortcode.preview ? (\n\t\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\t\tclassName=\"preview-component\"\n\t\t\t\t\t\t\t\t\t\tstyle={{ overflow: \"visible\" }}\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<shortcode.preview />\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t) : (\n\t\t\t\t\t\t\t\t\t<div className=\"preview-placeholder\">\n\t\t\t\t\t\t\t\t\t\t<span>📊</span>\n\t\t\t\t\t\t\t\t\t\t<small>{__(\"Preview\", \"trustpilot-reviewkit\")}</small>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t<p className=\"shortcode-description\">{shortcode.description}</p>\n\n\t\t\t\t\t\t\t<div className=\"shortcode-code\">\n\t\t\t\t\t\t\t\t<code>{shortcode.shortcode}</code>\n\t\t\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\t\t\tvariant=\"secondary\"\n\t\t\t\t\t\t\t\t\tsize=\"small\"\n\t\t\t\t\t\t\t\t\tonClick={() => copyToClipboard(shortcode.shortcode)}\n\t\t\t\t\t\t\t\t\tclassName={\n\t\t\t\t\t\t\t\t\t\tcopiedShortcode === shortcode.shortcode ? \"copied\" : \"\"\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{copiedShortcode === shortcode.shortcode\n\t\t\t\t\t\t\t\t\t\t? __(\"Copied!\", \"trustpilot-reviewkit\")\n\t\t\t\t\t\t\t\t\t\t: __(\"Copy\", \"trustpilot-reviewkit\")}\n\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t{/* {shortcode.attributes && shortcode.attributes.length > 0 && (\n\t\t\t\t\t\t\t\t<details className=\"shortcode-attributes\">\n\t\t\t\t\t\t\t\t\t<summary>{__(\"Attributes\", \"trustpilot-reviewkit\")}</summary>\n\t\t\t\t\t\t\t\t\t<div className=\"attributes-list\">\n\t\t\t\t\t\t\t\t\t\t{shortcode.attributes.map((attr, index) => (\n\t\t\t\t\t\t\t\t\t\t\t<div key={index} className=\"attribute-item\">\n\t\t\t\t\t\t\t\t\t\t\t\t<strong>{attr.name}</strong>\n\t\t\t\t\t\t\t\t\t\t\t\t<span className=\"attribute-default\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t({attr.default})\n\t\t\t\t\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t\t\t\t\t\t<p>{attr.description}</p>\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t))}\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</details>\n\t\t\t\t\t\t\t)} */}\n\t\t\t\t\t\t</CardBody>\n\t\t\t\t\t</Card>\n\t\t\t\t))}\n\t\t\t</div>\n\n\t\t\t{filteredShortcodes.length === 0 && (\n\t\t\t\t<div className=\"no-results\">\n\t\t\t\t\t<p>\n\t\t\t\t\t\t{__(\n\t\t\t\t\t\t\t\"No shortcodes found matching your search.\",\n\t\t\t\t\t\t\t\"trustpilot-reviewkit\"\n\t\t\t\t\t\t)}\n\t\t\t\t\t</p>\n\t\t\t\t</div>\n\t\t\t)}\n\t\t</div>\n\t);\n};\n\nexport default ShortCodes;\n", "import { __ } from \"@wordpress/i18n\";\nimport SingleImage from \"../containers/preview/image\";\nimport { data, formatReviewCount } from \"../helper/utils\";\n\n/**\n * Micro Button Preview Component\n * Mirrors the exact HTML structure from Reviews-kit/microbutton.html\n */\nconst MicroButtonPreview = ({ reviews }) => {\n\tconst businessData = data?.business_details || {};\n\tconst dynamicReviews = reviews || businessData.numberOfReviews || 1300;\n\n\treturn (\n\t\t<div className=\"reviewkit_fpln_mcb_wrap\">\n\t\t\t<a className=\"reviewkit_fpln_mcb_left\" href=\"#\">\n\t\t\t\t<SingleImage type=\"light\" />\n\t\t\t\t<span className=\"place_name\">{__(\"Trustpilot\", \"reviewkit\")}</span>\n\t\t\t</a>\n\n\t\t\t<div className=\"reviewkit_fpln_mcb_right\">\n\t\t\t\t{formatReviewCount(dynamicReviews)} {__(\"reviews\", \"reviewkit\")}\n\t\t\t</div>\n\t\t</div>\n\t);\n};\n\nexport default MicroButtonPreview;\n", "import {\n\tdata,\n\tformatReviewCount,\n\tgetRatingText,\n\tgetStarImageUrl,\n} from \"../helper/utils\";\nimport SingleImage from \"../containers/preview/image\";\nimport { __ } from \"@wordpress/i18n\";\n\n/**\n * Micro Combo Preview Component\n * Mirrors the exact HTML structure from Reviews-kit/microcombo.html\n */\nconst MicroComboPreview = ({ rating, stars, reviews }) => {\n\tconst businessData = data?.business_details || {};\n\tconst dynamicScore = businessData.trustScore || 4.8;\n\tconst dynamicReviews = reviews || businessData.numberOfReviews || 437;\n\n\tconst dynamicRating = rating || getRatingText(dynamicScore);\n\n\treturn (\n\t\t<div className=\"reviewkit_fpln_mc reviewkit_fpln_common preview\">\n\t\t\t<div className=\"reviewkit_fpln_mc_inner_left\">\n\t\t\t\t<span className=\"review_us_one\">{dynamicRating}</span>\n\t\t\t\t<div className=\"reviewkit_star_rating\">\n\t\t\t\t\t<img\n\t\t\t\t\t\tsrc={getStarImageUrl(dynamicScore)}\n\t\t\t\t\t\talt={`${dynamicRating} star rating`}\n\t\t\t\t\t\tstyle={{ width: \"100%\" }}\n\t\t\t\t\t/>\n\t\t\t\t</div>\n\t\t\t</div>\n\n\t\t\t<div className=\"reviewkit_fpln_mc_inner_right\">\n\t\t\t\t<span className=\"mirc_r_count\">\n\t\t\t\t\t{formatReviewCount(dynamicReviews)}\n\t\t\t\t</span>\n\t\t\t\t<span className=\"review_us_one\">{__(\"reviews on\", \"reviewkit\")}</span>\n\t\t\t\t<SingleImage style={{ width: \"18px\" }} />\n\t\t\t\t<span className=\"place_name\">{__(\"Trustpilot\", \"reviewkit\")}</span>\n\t\t\t</div>\n\t\t</div>\n\t);\n};\n\nexport default MicroComboPreview;\n", "import { data, formatReviewCount } from \"../helper/utils\";\nimport SingleImage from \"../containers/preview/image\";\nimport { __ } from \"@wordpress/i18n\";\n/**\n * Micro Review Count Preview Component\n * Mirrors the exact HTML structure from Reviews-kit/microreviewcount.html\n */\nconst MicroReviewCountPreview = ({ count }) => {\n\t// Get dynamic business data\n\tconst businessData = data?.business_details || {};\n\tconst dynamicCount = count || businessData.numberOfReviews || 437;\n\n\treturn (\n\t\t<div>\n\t\t\t<a className=\"reviewkit_fpln_mirc reviewkit_fpln_common preview\" href=\"#\">\n\t\t\t\t<span className=\"mirc_see\">{__(\"See our\", \"reviewkit\")}</span>\n\t\t\t\t<span className=\"mirc_r_count\">{formatReviewCount(dynamicCount)}</span>\n\t\t\t\t<span className=\"review_us_one\">{__(\"reviews on\", \"reviewkit\")}</span>\n\t\t\t\t<SingleImage style={{ width: \"20px\" }} />\n\t\t\t\t<span className=\"place_name\">{__(\"Trustpilot\", \"reviewkit\")}</span>\n\t\t\t</a>\n\t\t</div>\n\t);\n};\n\nexport default MicroReviewCountPreview;\n", "import { __ } from \"@wordpress/i18n\";\nimport { data, getRatingText, getStarImageUrl } from \"../helper/utils\";\nimport SingleImage from \"../containers/preview/image\";\n\n/**\n * Micro Star Preview Component\n * Mirrors the exact HTML structure from Reviews-kit/microstar.html\n */\nconst MicroStarPreview = ({ rating, stars }) => {\n\tconst businessData = data?.business_details || {};\n\tconst dynamicScore = businessData.trustScore || 4.8;\n\n\tconst dynamicRating = rating || getRatingText(dynamicScore);\n\n\treturn (\n\t\t<div className=\"reviewkit_fpln_mcs reviewkit_fpln_common preview\">\n\t\t\t<div className=\"dynamic_rating\">{dynamicRating}</div>\n\t\t\t<div className=\"dynamic_stars\">\n\t\t\t\t<img\n\t\t\t\t\tsrc={getStarImageUrl(dynamicScore)}\n\t\t\t\t\talt={`${dynamicRating} star rating`}\n\t\t\t\t\tstyle={{ width: \"100%\" }}\n\t\t\t\t/>\n\t\t\t</div>\n\t\t\t<div className=\"tp-wrapper\">\n\t\t\t\t<SingleImage />\n\t\t\t\t<div className=\"place_name\">{__(\"Trustpilot\", \"reviewkit\")}</div>\n\t\t\t</div>\n\t\t</div>\n\t);\n\n\t// return (\n\t// \t<div className=\"reviewkit_fpln_mcs reviewkit_fpln_common preview\">\n\t// \t\t{/* Left section with rating and stars */}\n\t// \t\t<div className=\"reviewkit_fpln_mc_inner_left\">\n\t// \t\t\t<span className=\"review_us_one\">{dynamicRating}</span>\n\t// \t\t\t<div className=\"reviewkit_star_rating\">\n\t// \t\t\t\t<img\n\t// \t\t\t\t\tsrc={getStarImageUrl(dynamicScore)}\n\t// \t\t\t\t\talt={`${dynamicRating} star rating`}\n\t// \t\t\t\t\tstyle={{ width: \"100%\" }}\n\t// \t\t\t\t/>\n\t// \t\t\t</div>\n\t// \t\t</div>\n\n\t// \t\t{/* Right section with Trustpilot logo */}\n\t// \t\t<div className=\"reviewkit_fpln_mc_inner_right\">\n\t// \t\t\t<span className=\"review_us_one\"></span>\n\t// \t\t\t<svg className=\"reviewkit_fpln_star_icon\" viewBox=\"0 0 24 24\">\n\t// \t\t\t\t<path d=\"M12 2l2.938 7.968h8.382l-6.76 4.898 2.553 7.834L12 17.334l-7.113 5.366 2.553-7.834-6.76-4.898h8.382z\" />\n\t// \t\t\t</svg>\n\t// \t\t\t<span className=\"place_name\">Trustpilot</span>\n\t// \t\t</div>\n\t// \t</div>\n\t// );\n};\n\nexport default MicroStarPreview;\n", "import React from \"react\";\nimport { data } from \"../helper/utils\";\nimport SingleImage from \"../containers/preview/image\";\n\n/**\n * Micro TrustScore Preview Component\n * Mirrors the exact HTML structure from Reviews-kit/microtrustscore.html\n */\nconst MicroTrustScorePreview = ({ rating, score }) => {\n\t// Get dynamic business data\n\tconst businessData = data?.business_details || {};\n\tconst dynamicScore = score || businessData.trustScore || 4.8;\n\n\t// Get rating text based on score\n\tconst getRatingText = (score) => {\n\t\tif (score >= 4.5) return \"Excellent\";\n\t\tif (score >= 4.0) return \"Great\";\n\t\tif (score >= 3.0) return \"Good\";\n\t\tif (score >= 2.0) return \"Average\";\n\t\treturn \"Poor\";\n\t};\n\n\tconst dynamicRating = rating || getRatingText(dynamicScore);\n\treturn (\n\t\t<div className=\"reviewkit_fpln_mcts reviewkit_fpln_common preview\">\n\t\t\t{/* Left section with rating and score */}\n\t\t\t<div className=\"reviewkit_fpln_inner_left\">\n\t\t\t\t<span className=\"review_us_one\">{dynamicRating}</span>\n\n\t\t\t\t<div className=\"reviewkit_score\">\n\t\t\t\t\t<span className=\"reviewkit_orignal_rcount\">{dynamicScore}</span>\n\t\t\t\t\t<span className=\"reviewkit_out_of\"> out of 5</span>\n\t\t\t\t</div>\n\t\t\t</div>\n\n\t\t\t{/* Right section with Trustpilot logo */}\n\t\t\t<div className=\"reviewkit_fpln_inner_right\">\n\t\t\t\t<SingleImage />\n\t\t\t\t<span className=\"place_name\">Trustpilot</span>\n\t\t\t</div>\n\t\t</div>\n\t);\n};\n\nexport default MicroTrustScorePreview;\n", "import SingleImage from \"../containers/preview/image\";\nimport {\n\tdata,\n\tformatReviewCount,\n\tgetStarImageUrl,\n\tplugin_root_url,\n} from \"../helper/utils\";\nimport { __ } from \"@wordpress/i18n\";\n\n/**\n * Mini Trustbox Preview Component\n * Mirrors the exact HTML structure from Reviews-kit/mini.html\n */\nconst MiniTrustboxPreview = ({ trustscore, reviews }) => {\n\tconst businessData = data?.business_details || {};\n\tconst dynamicTrustScore = trustscore || businessData.trustScore || 4.8;\n\tconst dynamicReviews = reviews || businessData.numberOfReviews || 347;\n\n\treturn (\n\t\t<div className=\"reviewkit_fpln_mini preview\">\n\t\t\t<div className=\"reviewkit_fpln_inner_top\">\n\t\t\t\t<SingleImage />\n\t\t\t\t<span className=\"place_name\">{__(\"Trustpilot\", \"reviewkit\")}</span>\n\t\t\t</div>\n\n\t\t\t<div className=\"reviewkit_bg\">\n\t\t\t\t<img\n\t\t\t\t\tsrc={getStarImageUrl(dynamicTrustScore)}\n\t\t\t\t\talt={`${dynamicTrustScore} star rating`}\n\t\t\t\t/>\n\t\t\t</div>\n\n\t\t\t{/* Bottom section with score and reviews */}\n\t\t\t<div className=\"reviewkit_fpln_inner_bottom\">\n\t\t\t\t<div className=\"reviewkit_left_reviews\">\n\t\t\t\t\t<span className=\"review_us_one\">\n\t\t\t\t\t\t{__(\"TrustScore\", \"reviewkit\")}{\" \"}\n\t\t\t\t\t</span>\n\t\t\t\t\t<span className=\"reviewkit_orignal_rcount\">{dynamicTrustScore}</span>\n\t\t\t\t</div>\n\n\t\t\t\t<div className=\"reviewkit_review_area\">\n\t\t\t\t\t<span className=\"reviewkit_out_of\">\n\t\t\t\t\t\t{formatReviewCount(dynamicReviews)}\n\t\t\t\t\t</span>\n\t\t\t\t\t<span className=\"reviewkit_reviews\">\n\t\t\t\t\t\t{\" \"}\n\t\t\t\t\t\t{__(\"reviews\", \"reviewkit\")}\n\t\t\t\t\t</span>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>\n\t);\n};\n\nexport default MiniTrustboxPreview;\n", "import {\n\tdata,\n\tformatReviewCount,\n\tgetStarImageUrl,\n\tplugin_root_url,\n} from \"../helper/utils\";\nimport { __ } from \"@wordpress/i18n\";\n/**\n * Starter Trustbox Preview Component\n * Mirrors the exact HTML structure from Reviews-kit/starter.html\n */\nconst StarterTrustboxPreview = ({ reviews }) => {\n\tconst businessData = data?.business_details || {};\n\tconst dynamicReviews = reviews || businessData.numberOfReviews || 1376;\n\tconst dynamicTrustScore = businessData.trustScore || 4.8;\n\n\treturn (\n\t\t<div className=\"reviewkit_fpln_starter preview\">\n\t\t\t<div className=\"reviewkit_fpln_inner_top\">\n\t\t\t\t<span className=\"mirc_see\">{__(\"Check out our\", \"reviewkit\")} </span>\n\t\t\t\t<span className=\"mirc_r_count\">\n\t\t\t\t\t{formatReviewCount(dynamicReviews)}\n\t\t\t\t</span>\n\t\t\t\t<span className=\"review_us_one\"> {__(\"reviews\", \"reviewkit\")}</span>\n\t\t\t</div>\n\n\t\t\t<div className=\"reviewkit_bg\">\n\t\t\t\t<img\n\t\t\t\t\tsrc={getStarImageUrl(dynamicTrustScore)}\n\t\t\t\t\talt={`${dynamicTrustScore} star rating`}\n\t\t\t\t/>\n\t\t\t</div>\n\n\t\t\t<div className=\"reviewkit_toltip_wrap\">\n\t\t\t\t<div className=\"reviewkit_logo_container\">\n\t\t\t\t\t<Tooltip />\n\n\t\t\t\t\t<img\n\t\t\t\t\t\tsrc={plugin_root_url + \"/assets/images/single-star.svg\"}\n\t\t\t\t\t\talt=\"Trustpilot Logo\"\n\t\t\t\t\t/>\n\t\t\t\t\t<span className=\"reviewkit_text\">Trustpilot</span>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>\n\t);\n};\n\nexport default StarterTrustboxPreview;\n\nconst Tooltip = () => (\n\t<div className=\"reviewkit_tooltip reviewkit_tooltip_large\">\n\t\t<div className=\"reviewkit_tooltip_content\">\n\t\t\t{__(\"Helping each other make better choices\", \"reviewkit\")}\n\t\t</div>\n\t\t<a href=\"#\" className=\"reviewkit_tooltip_link\">\n\t\t\t{__(\"Read and write reviews\", \"reviewkit\")}\n\t\t</a>\n\t</div>\n);\n", "module.exports = window[\"wp\"][\"components\"];", "module.exports = window[\"wp\"][\"i18n\"];", "module.exports = window[\"React\"];", "module.exports = window[\"ReactDOM\"];", "module.exports = window[\"ReactJSXRuntime\"];", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import React from \"react\";\nimport { createRoot } from \"react-dom\";\nimport App from \"./components/App\";\nimport \"./assets/scss/main.scss\";\n// import \"./assets/scss/previews/main.scss\"\nimport { DataContextProvider } from \"./components/context/data-context\";\n\ndocument.addEventListener(\"DOMContentLoaded\", function () {\n  const body = document.getElementById(\"trustpilot-reviewskit-body\");\n  const root = createRoot(body);\n\n  root.render(\n    <DataContextProvider>\n      <App />\n    </DataContextProvider>,\n  );\n});\n"], "names": ["React", "Layout", "useDataContext", "ShortCodes", "Reviews", "jsx", "_jsx", "jsxs", "_jsxs", "App", "tab", "children", "plugin_root_url", "SingleImage", "type", "rest", "src", "alt", "style", "width", "CustomerInfo", "customer", "size", "showImage", "name", "image", "getInitials", "split", "map", "word", "char<PERSON>t", "join", "toUpperCase", "slice", "className", "onError", "e", "target", "display", "nextS<PERSON>ling", "StarRating", "getRatingText", "getStarImageUrl", "ReviewCard", "review", "reviewId", "rating", "reviewTitle", "reviewBody", "dates", "reviewUrl", "console", "info", "publishedDate", "experiencedDate", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "error", "dynamicRating", "truncateText", "text", "max<PERSON><PERSON><PERSON>", "length", "substring", "href", "rel", "useState", "__", "ReviewList", "reviews", "initialDisplayCount", "showLoadMore", "displayCount", "setDisplayCount", "reviewsArray", "Object", "values", "sort", "a", "b", "dateA", "dateB", "totalReviews", "displayedReviews", "hasMoreReviews", "handleLoadMore", "prev", "Math", "min", "handleShowLess", "ratingFilter", "setRatingFilter", "filteredReviews", "filter", "getRatingDistribution", "distribution", "for<PERSON>ach", "floor", "ratingDistribution", "onClick", "maxStars", "showRating", "useSvg", "stars", "fullStars", "hasHalfStar", "getTrustpilotColor", "starColor", "TrustpilotSvgStar", "filled", "color", "viewBox", "fill", "d", "i", "push", "position", "top", "left", "overflow", "emptyStars", "TrustpilotBusinessPage", "businessDetails", "displayName", "identifyingName", "numberOfReviews", "trustScore", "websiteUrl", "profileImageUrl", "categories", "activity", "formatNumber", "num", "toFixed", "toString", "primaryCategory", "find", "cat", "isPrimary", "isVerified", "verification", "verifiedUserIdentity", "isClaimed", "getImageUrl", "url", "startsWith", "reply<PERSON><PERSON><PERSON><PERSON>", "replyPercentage", "averageDaysToReply", "round", "createContext", "useContext", "DataContext", "data", "reviewDetails", "DataContextProvider", "setTab", "setData", "value", "Provider", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nonce", "count", "last_updated", "domain", "original_domain", "window", "trustpilot_reviewkit", "getReviewsFromAPI", "options", "Error", "cleanDomain", "replace", "timeout", "signal", "controller", "AbortController", "timeoutId", "setTimeout", "abort", "response", "fetch", "encodeURIComponent", "method", "headers", "Accept", "clearTimeout", "ok", "status", "statusText", "json", "getReviews", "Promise", "reject", "resolve", "formData", "FormData", "append", "revalidate", "j<PERSON><PERSON><PERSON>", "ajax", "processData", "contentType", "success", "message", "xhr", "body", "then", "catch", "formatReviewCount", "roundedRating", "filename", "score", "menuItems", "item", "label", "TextControl", "<PERSON><PERSON>", "Spinner", "Fragment", "_Fragment", "business_details", "trustpilotUrl", "setTrustpilotUrl", "loading", "setLoading", "__fetchReviews", "__nextHasNoMarginBottom", "__next40pxDefaultSize", "help", "onChange", "variant", "backgroundColor", "fontWeight", "disabled", "marginTop", "textAlign", "height", "Card", "CardBody", "<PERSON><PERSON><PERSON><PERSON>", "MiniTrustboxPreview", "StarterTrustboxPreview", "MicroTrustScorePreview", "MicroStarPreview", "MicroReviewCountPreview", "MicroButtonPreview", "MicroComboPreview", "searchTerm", "setSearchTerm", "copiedShortcode", "setCopiedShortcode", "shortcodes", "id", "shortcode", "description", "category", "preview", "attributes", "default", "filteredShortcodes", "toLowerCase", "includes", "copyToClipboard", "navigator", "clipboard", "isSecureContext", "writeText", "textArea", "document", "createElement", "append<PERSON><PERSON><PERSON>", "focus", "select", "successful", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "err", "alert", "alignItems", "justifyContent", "placeholder", "businessData", "dynamicReviews", "dynamicScore", "dynamicCount", "trustscore", "dynamicTrustScore", "<PERSON><PERSON><PERSON>", "createRoot", "addEventListener", "getElementById", "root", "render"], "sourceRoot": ""}