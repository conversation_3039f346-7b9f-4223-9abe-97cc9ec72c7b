{"version": 3, "file": "trustpilot-reviewskit.core.min.css", "mappings": ";;;AAAA,gBAAgB;AAAhB;ACCA;EACE;EACA;EACA;ADEF;ACAE;EACE;EACA;EACA;ADEJ;ACCE;EACE;EACA;EACA;EACA;KAAA;UAAA;ADCJ;ACKI;EACE;ADHN;ACKM;EACE;EACA;EACA;EACA;EACA;EACA;ADHR;ACOI;EACE;ADLN;ACSI;EACE;ADPN;ACYE;EACE;EACA;ADVJ;ACgBI;EACE;ADdN;ACkBI;EACE;ADhBN;ACoBE;EACE;EACA;ADlBJ;ACqBE;EACE;EACA;EACA;EACA;ADnBJ;ACuBE;EACE;ADrBJ;ACuBI;EACE;ADrBN;ACwBI;EACE;ADtBN;ACyBI;EACE;EACA;ADvBN;AC0BI;EACE;ADxBN;AC4BE;EACE;AD1BJ;AC4BI;EACE;AD1BN;AC6BI;EACE;AD3BN;AC8BI;EACE;EACA;AD5BN;AC+BI;EACE;AD7BN;ACiCE;EACE;AD/BJ;ACiCI;EACE;AD/BN;ACkCI;EACE;ADhCN;ACmCI;EACE;EACA;ADjCN;ACoCI;EACE;EACA;ADlCN;;AEtGA;EACE;EACA;EACA;AFyGF;AEvGE;EACE;EACA;EACA;EACA;EACA;EACA;AFyGJ;AEvGI;EACE;EACA;EACA;KAAA;EACA;AFyGN;AEtGI;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AFwGN;AEpGE;EACE;EACA;AFsGJ;AEpGI;EACE;EACA;EACA;EACA;EACA;EACA;AFsGN;AEjGE;EACE;AFmGJ;AEjGI;EACE;EACA;AFmGN;AEjGM;EACE;AFmGR;AE9FM;EACE;AFgGR;AE3FE;EACE;AF6FJ;AE3FI;EACE;EACA;AF6FN;AE3FM;EACE;AF6FR;AExFM;EACE;AF0FR;AErFE;EACE;AFuFJ;AErFI;EACE;EACA;AFuFN;AErFM;EACE;AFuFR;AElFM;EACE;EACA;AFoFR;;AGzLA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;AH4LF;AG1LE;EACE;EACA;AH4LJ;AGzLE;EACE;EACA;EACA;EACA;EACA;EACA;AH2LJ;AGzLI;EACE;EACA;EACA;EACA;AH2LN;AGzLM;EACE;EACA;EACA;AH2LR;AGrLI;EACE;EACA;EACA;EACA;EACA;AHuLN;AGpLI;EACE;EACA;EACA;EACA;EACA;AHsLN;AGnLI;EACE;AHqLN;AGnLM;EACE;EACA;AHqLR;AGhLE;EACE;EACA;EACA;AHkLJ;AGhLI;EACE;EACA;EACA;EACA;EACA;AHkLN;AGhLM;EACE;EACA;AHkLR;AG/KM;EACE;EACA;AHiLR;AG3KE;EAxFF;IAyFI;IACA;EH8KF;EG5KE;IACE;IACA;IACA;EH8KJ;EG5KI;IACE;IACA;IACA;EH8KN;EGzKI;IACE;EH2KN;EGxKI;IACE;EH0KN;AACF;;AIzRA;EACE;EACA;EACA;EACA;EACA;EACA;AJ4RF;AI1RE;EACE;EACA;EACA;AJ4RJ;AI1RI;EACE;EACA;EACA;AJ4RN;AIxRE;EACE;EACA;EACA;AJ0RJ;AIxRI;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AJ0RN;AIxRM;EACE;EACA;EACA;KAAA;AJ0RR;AIvRM;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AJyRR;AIrRI;EACE;EACA;AJuRN;AIrRM;EACE;EACA;EACA;EACA;EACA;AJuRR;AIrRQ;EACE;EACA;EACA;EACA;EACA;AJuRV;AIpRQ;;EAEE;EACA;EACA;EACA;EACA;EACA;EACA;AJsRV;AInRQ;EACE;AJqRV;AIjRM;EACE;EACA;EACA;AJmRR;AIhRM;EACE;EACA;EACA;EACA;AJkRR;AI/QM;EACE;AJiRR;AI/QQ;EACE;EACA;EACA;OAAA;EACA;AJiRV;AI/QU;EACE;EACA;AJiRZ;AI/QY;EACE;AJiRd;AI7QU;EACE;EACA;AJ+QZ;AI3QQ;EACE;EACA;EACA;AJ6QV;AIxQQ;EAIE;EACA;EAEA;EAGA;AJoQV;AIlQU;EACE;EACA;AJoQZ;AIjQU;EACE;EACA;EACA;AJmQZ;AI5PE;EACE;EACA;EACA;EACA;EACA;EACA;AJ8PJ;AI5PI;EACE;EACA;EACA;AJ8PN;AI5PM;EACE;EACA;EACA;EACA;AJ8PR;AI3PM;EACE;EACA;EACA;AJ6PR;AIvPE;EA9LF;IA+LI;EJ0PF;EIxPE;IACE;IACA;IACA;EJ0PJ;EIxPI;IACE;EJ0PN;EItPM;IACE;EJwPR;EItPQ;IACE;EJwPV;EIlPE;IACE;IACA;EJoPJ;EIlPI;IACE;EJoPN;AACF;;AK/cA;EACE;ALkdF;AKjdE;EACE;ALmdJ;AKjdI;EACE;EACA;EACA;EACA;ALmdN;AK/cM;EACE;EACA;EACA;ALidR;AK/cQ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ALidV;AK/cU;EACE;EACA;ALidZ;AK9cU;EACE;EACA;EACA;ALgdZ;AKzcE;EACE;EACA;EACA;EACA;EACA;EACA;AL2cJ;AKzcI;EACE;EACA;EACA;EACA;AL2cN;AKjcE;EACE;EACA;EACA;EACA;EACA;EACA;ALmcJ;AKjcI;;EAEE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ALmcN;AKjcM;;EACE;EACA;ALocR;AKhcI;EACE;EACA;ALkcN;AKhcM;EACE;EACA;ALkcR;AK7bE;EACE;EACA;AL+bJ;AK7bI;EACE;EACA;EACA;AL+bN;AK1bE;EAEI;IACE;EL2bN;EKvbM;IACE;ELybR;EKvbQ;IACE;IACA;ELybV;EKnbE;IACE;IACA;ELqbJ;EKnbI;;IAEE;IACA;ELqbN;AACF;;AA3jBA;EACE;AA8jBF;AA5jBE;EACE;EACA;EACA;AA8jBJ;AA5jBI;EACE;EACA;OAAA;EACA;EACA;EACA;AA8jBN;AA5jBM;EACE;EACA;EACA;EACA;EACA;AA8jBR;AA5jBQ;EACE;AA8jBV;AAxjBE;EACE;AA0jBJ;AAtjBQ;EACE;EACA;EACA;OAAA;AAwjBV;AAvjBU;EACE;AAyjBZ;AApjBM;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;OAAA;AAsjBR;AApjBQ;EACE;EACA;EACA;EACA;EACA;EACA;AAsjBV;AApjBU;EACE;EACA;KAAA;AAsjBZ;AAjjBU;EACE;EACA;AAmjBZ;AAjjBU;EACE;AAmjBZ;AA9iBM;EAEE;AA+iBR;AA5iBU;EACE;AA8iBZ;AA5iBU;EACE;EACA;OAAA;EACA;EACA;AA8iBZ;AA3iBc;EACE;EACA;AA6iBhB;AAxiBc;EACE;AA0iBhB;AAriBU;EACE;AAuiBZ;AA/hBM;EACE;EACA;EACA;EACA;AAiiBR;AA/hBQ;EACE;EACA;AAiiBV;AA9hBQ;EACE;EACA;AAgiBV;AA7hBQ;EACE;AA+hBV;AA9hBU;EACE;AAgiBZ;AA3hBM;EACE;EACA;EACA;EACA;AA6hBR;AA3hBQ;EACE;EACA;EACA;AA6hBV;AA3hBU;EACE;AA6hBZ;AA1hBU;EACE;AA4hBZ;AAzhBU;EACE;EACA;EACA;EACA;EACA;AA2hBZ;AAzhBY;EACE;EACA;EACA;AA2hBd;AAxhBY;EACE;EACA;EACA;EACA;EACA;EACA;EACA;AA0hBd;AAthBU;EACE;AAwhBZ;AAthBY;EACE;AAwhBd;AAthBc;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGQ;AAshBxB;AAnhBc;EACE;EACA;EACA;EACA;EACA;EACA;AAqhBhB;AAnhBgB;EACE;EACA;EACA;AAqhBlB;AAlhBgB;EACE;EACA;EACA;AAohBlB;AA/gBY;EACE;EACA;EACA;AAihBd;AA9gBY;EACE;EACA;EACA;EACA;EACA;EACA;EACA;AAghBd;AA9gBc;EACE;EACA;EACA;EACA;EACA;EACA;AAghBhB;AA5gBgB;EACE;EACA;EACA;AA8gBlB;AAxgBc;EACE;EACA;EACA;EACA;AA0gBhB;AAxgBgB;EACE;AA0gBlB;AAtgBc;EACE;AAwgBhB;AAtgBgB;EACE;EACA;EACA;AAwgBlB;AAtgBkB;EACE;EACA;AAwgBpB;AArgBkB;EACE;EACA;EACA;AAugBpB;AApgBkB;EACE;EACA;EACA;AAsgBpB;AAngBkB;EACE;EACA;EACA;AAqgBpB;AA5fM;EACE;EACA;EACA;AA8fR;AA5fQ;EACE;EACA;AA8fV;AAzfM;EACE;IACE;IACA;EA2fR;EAvfQ;IACE;IACA;IACA;EAyfV;EAtfQ;IACE;IACA;EAwfV;EAtfU;IACE;EAwfZ;AACF,C", "sources": ["webpack://trustpilot-reviewkit/./react_app/assets/scss/main.scss", "webpack://trustpilot-reviewkit/./react_app/assets/scss/_star-rating.scss", "webpack://trustpilot-reviewkit/./react_app/assets/scss/_customer-info.scss", "webpack://trustpilot-reviewkit/./react_app/assets/scss/_review-card.scss", "webpack://trustpilot-reviewkit/./react_app/assets/scss/_trustpilot-business.scss", "webpack://trustpilot-reviewkit/./react_app/assets/scss/_review-list.scss"], "sourcesContent": ["/* @use \"../../../node_modules/@picocss/pico/scss/pico.scss\"; */\n\n// Import component styles\n@import \"star-rating\";\n@import \"customer-info\";\n@import \"review-card\";\n@import \"trustpilot-business\";\n@import \"review-list\";\n\n#trustpilot-reviewskit-body {\n  margin-right: 20px;\n\n  nav {\n    background: white;\n    margin-top: 20px;\n    padding: 0 20px;\n\n    ul {\n      display: flex;\n      column-gap: 20px;\n      padding: 20px 10px;\n      align-items: center;\n      margin: 0;\n\n      li {\n        color: black;\n        cursor: pointer;\n        font-weight: 600;\n        font-size: 16px;\n        margin-bottom: 0px;\n\n        &.active {\n          color: #007bff;\n        }\n      }\n    }\n  }\n\n  .page {\n    margin-top: 20px;\n\n    &.review-page {\n      > .review-fetch {\n        .button-container {\n          /* margin-top: 10px; */\n          display: flex;\n          column-gap: 10px;\n          > button {\n            margin-top: 10px;\n          }\n        }\n      }\n\n      .review-page-details {\n        border-radius: 8px;\n        overflow: hidden;\n        background-color: #fff;\n        border-radius: 5px;\n        /* padding: 20px; */\n        margin-top: 20px;\n        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n        height: 150px;\n        display: flex;\n        column-gap: 20px;\n\n        > .image-wrapper {\n          width: 150px;\n          position: relative;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          padding: 8px;\n\n          > img {\n            width: 100%;\n            object-fit: contain;\n          }\n        }\n\n        > .business-details {\n          h3 {\n            font-size: 22px;\n            margin-bottom: 5px;\n          }\n          p {\n            margin: 0;\n          }\n        }\n      }\n\n      .review-data {\n        // @import \"card.scss\";\n        margin-top: 20px;\n\n        .cardlist {\n          .card {\n            padding: 0;\n          }\n          .review-header {\n            display: flex;\n            column-gap: 10px;\n            border-bottom: 1px solid gray;\n            padding: 10px;\n\n            > .image-wrapper {\n              img {\n                width: 50px;\n                height: 50px;\n              }\n            }\n\n            > .name-wrapper {\n              > * {\n                margin: 0;\n              }\n            }\n          }\n\n          .review-content {\n            padding: 10px;\n          }\n        }\n      }\n    }\n\n    // Shortcodes page styles\n    &.shortcodes-page {\n      .shortcodes-header {\n        margin-bottom: 30px;\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n\n        h2 {\n          font-size: 24px;\n          margin-bottom: 10px;\n        }\n\n        p {\n          color: #666;\n          margin-bottom: 20px;\n        }\n\n        .shortcodes-search {\n          max-width: 400px;\n          > div {\n            margin-bottom: 0;\n          }\n        }\n      }\n\n      .shortcodes-grid {\n        display: grid;\n        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n        gap: 20px;\n        margin-bottom: 30px;\n\n        .shortcode-card {\n          border: 1px solid #ddd;\n          border-radius: 8px;\n          transition: box-shadow 0.2s ease;\n\n          &.micro_combo {\n            width: 600px;\n          }\n\n          &:hover {\n            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n          }\n\n          .components-card__header {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            padding: 16px 20px;\n            border-bottom: 1px solid #f0f0f0;\n\n            h3 {\n              margin: 0;\n              font-size: 18px;\n              font-weight: 600;\n            }\n\n            .shortcode-category {\n              background: #007bff;\n              color: white;\n              padding: 4px 8px;\n              border-radius: 12px;\n              font-size: 12px;\n              text-transform: uppercase;\n              font-weight: 500;\n            }\n          }\n\n          .components-card__body {\n            padding: 20px;\n\n            .shortcode-preview {\n              margin-bottom: 16px;\n\n              .preview-component {\n                background: #f8f9fa;\n                border: 1px solid #e9ecef;\n                border-radius: 6px;\n                padding: 16px;\n                min-height: 80px;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n                overflow: hidden;\n\n                // Import shortcode styles for previews\n                @import \"../../../assets/css/shortcodes.css\";\n              }\n\n              .preview-placeholder {\n                background: #f8f9fa;\n                border: 2px dashed #ddd;\n                border-radius: 6px;\n                padding: 20px;\n                text-align: center;\n                color: #666;\n\n                span {\n                  font-size: 24px;\n                  display: block;\n                  margin-bottom: 8px;\n                }\n\n                small {\n                  font-size: 12px;\n                  text-transform: uppercase;\n                  letter-spacing: 0.5px;\n                }\n              }\n            }\n\n            .shortcode-description {\n              color: #555;\n              margin-bottom: 16px;\n              line-height: 1.5;\n            }\n\n            .shortcode-code {\n              display: flex;\n              align-items: center;\n              gap: 10px;\n              margin-bottom: 16px;\n              padding: 12px;\n              background: #f8f9fa;\n              border-radius: 6px;\n\n              code {\n                flex: 1;\n                background: none;\n                padding: 0;\n                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n                font-size: 13px;\n                color: #d63384;\n              }\n\n              .components-button {\n                &.copied {\n                  background: #28a745;\n                  color: white;\n                  border-color: #28a745;\n                }\n              }\n            }\n\n            .shortcode-attributes {\n              summary {\n                cursor: pointer;\n                font-weight: 500;\n                margin-bottom: 10px;\n                color: #007bff;\n\n                &:hover {\n                  text-decoration: underline;\n                }\n              }\n\n              .attributes-list {\n                padding-left: 16px;\n\n                .attribute-item {\n                  margin-bottom: 12px;\n                  padding-bottom: 8px;\n                  border-bottom: 1px solid #f0f0f0;\n\n                  &:last-child {\n                    border-bottom: none;\n                    margin-bottom: 0;\n                  }\n\n                  strong {\n                    color: #333;\n                    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n                    font-size: 13px;\n                  }\n\n                  .attribute-default {\n                    color: #666;\n                    font-size: 12px;\n                    margin-left: 8px;\n                  }\n\n                  p {\n                    margin: 4px 0 0 0;\n                    color: #555;\n                    font-size: 13px;\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n\n      .no-results {\n        text-align: center;\n        padding: 40px 20px;\n        color: #666;\n\n        p {\n          margin: 0;\n          font-style: italic;\n        }\n      }\n\n      // Responsive design\n      @media (max-width: 768px) {\n        .shortcodes-grid {\n          grid-template-columns: 1fr;\n          gap: 16px;\n        }\n\n        .shortcode-card {\n          .components-card__header {\n            flex-direction: column;\n            align-items: flex-start;\n            gap: 8px;\n          }\n\n          .shortcode-code {\n            flex-direction: column;\n            align-items: stretch;\n\n            .components-button {\n              width: 100%;\n            }\n          }\n        }\n      }\n    }\n  }\n}\n", "// Trustpilot Star Rating Component Styles - Authentic Design\n.trustpilot-star-rating {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n\n  .stars {\n    display: flex;\n    align-items: center;\n    gap: 3px; // Trustpilot uses tighter spacing\n  }\n\n  .star {\n    display: inline-block;\n    line-height: 1;\n    transition: all 0.3s ease;\n    user-select: none;\n\n    &.star-full {\n      // Color is set inline based on rating\n    }\n\n    &.star-half {\n      position: relative;\n\n      &::after {\n        content: \"★\";\n        position: absolute;\n        left: 50%;\n        top: 0;\n        color: #ddd;\n        z-index: -1;\n      }\n    }\n\n    &.star-empty {\n      color: #ddd;\n    }\n\n    // Hover effect for interactive feel\n    &:hover {\n      transform: scale(1.05);\n    }\n  }\n\n  // SVG Star styles for authentic Trustpilot look\n  .star-svg {\n    display: inline-block;\n    transition: all 0.3s ease;\n\n    &.star-filled {\n      // Fill color is set inline\n    }\n\n    &.star-empty {\n      fill: #ddd;\n    }\n\n    // Hover effect\n    &:hover {\n      transform: scale(1.05);\n    }\n  }\n\n  .star-svg-half {\n    display: inline-block;\n    position: relative;\n  }\n\n  .rating-value {\n    font-size: 0.9em;\n    color: #666;\n    font-weight: 500;\n    margin-left: 4px;\n  }\n\n  // Size variants matching Trustpilot patterns\n  &.trustpilot-star-rating--small {\n    gap: 6px;\n\n    .stars {\n      gap: 2px;\n    }\n\n    .star {\n      font-size: 14px;\n    }\n\n    .star-svg {\n      width: 14px;\n      height: 14px;\n    }\n\n    .rating-value {\n      font-size: 12px;\n    }\n  }\n\n  &.trustpilot-star-rating--medium {\n    gap: 8px;\n\n    .stars {\n      gap: 3px;\n    }\n\n    .star {\n      font-size: 18px;\n    }\n\n    .star-svg {\n      width: 18px;\n      height: 18px;\n    }\n\n    .rating-value {\n      font-size: 14px;\n    }\n  }\n\n  &.trustpilot-star-rating--large {\n    gap: 10px;\n\n    .stars {\n      gap: 4px;\n    }\n\n    .star {\n      font-size: 26px; // Matches Trustpilot's microstar size\n    }\n\n    .star-svg {\n      width: 26px;\n      height: 26px;\n    }\n\n    .rating-value {\n      font-size: 16px;\n      font-weight: 600;\n    }\n  }\n}\n\n// Legacy support - keep old class name working\n.star-rating {\n  @extend .trustpilot-star-rating;\n}\n", "// Customer Info Component Styles\n.customer-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n\n  .customer-avatar {\n    position: relative;\n    border-radius: 50%;\n    overflow: hidden;\n    background-color: #f5f5f5;\n    flex-shrink: 0;\n    box-shadow: rgba(0, 0, 0, 0.02) 0px 1px 3px 0px, rgba(27, 31, 35, 0.15) 0px 0px 0px 1px;\n\n    .customer-image {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n      border-radius: 50%;\n    }\n\n    .customer-initials {\n      width: 100%;\n      height: 100%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background: linear-gradient(135deg, #007bff, #0056b3);\n      color: white;\n      font-weight: 600;\n      border-radius: 50%;\n    }\n  }\n\n  .customer-details {\n    flex: 1;\n    min-width: 0; // Allow text truncation\n\n    .customer-name {\n      font-weight: 500;\n      color: #333;\n      display: block;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n  }\n\n  // Size variants\n  &.customer-info--small {\n    gap: 8px;\n\n    .customer-avatar {\n      width: 32px;\n      height: 32px;\n\n      .customer-initials {\n        font-size: 12px;\n      }\n    }\n\n    .customer-details {\n      .customer-name {\n        font-size: 14px;\n      }\n    }\n  }\n\n  &.customer-info--medium {\n    gap: 12px;\n\n    .customer-avatar {\n      width: 48px;\n      height: 48px;\n\n      .customer-initials {\n        font-size: 16px;\n      }\n    }\n\n    .customer-details {\n      .customer-name {\n        font-size: 16px;\n      }\n    }\n  }\n\n  &.customer-info--large {\n    gap: 16px;\n\n    .customer-avatar {\n      width: 64px;\n      height: 64px;\n\n      .customer-initials {\n        font-size: 20px;\n      }\n    }\n\n    .customer-details {\n      .customer-name {\n        font-size: 18px;\n        font-weight: 600;\n      }\n    }\n  }\n}\n", "// Review Card Component Styles\n.review-card {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  margin-bottom: 16px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  border: 1px solid #e9ecef;\n  transition: box-shadow 0.2s ease, transform 0.2s ease;\n\n  &:hover {\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\n    cursor: pointer;\n  }\n\n  .review-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: flex-start;\n    margin-bottom: 16px;\n    padding-bottom: 12px;\n    border-bottom: 1px solid #f0f0f0;\n\n    .review-meta {\n      display: flex;\n      flex-direction: column;\n      align-items: flex-end;\n      gap: 4px;\n\n      .review-date {\n        font-size: 12px;\n        color: #666;\n        white-space: nowrap;\n      }\n    }\n  }\n\n  .review-content {\n    .review-title {\n      font-size: 16px;\n      font-weight: 600;\n      color: #333;\n      margin: 0 0 8px 0;\n      line-height: 1.4;\n    }\n\n    .review-body {\n      font-size: 14px;\n      line-height: 1.6;\n      color: #555;\n      margin: 0 0 12px 0;\n      word-wrap: break-word;\n    }\n\n    .review-experience-date {\n      margin-top: 8px;\n\n      small {\n        color: #888;\n        font-size: 12px;\n      }\n    }\n  }\n\n  .review-footer {\n    margin-top: 16px;\n    padding-top: 12px;\n    border-top: 1px solid #f0f0f0;\n\n    .review-link {\n      color: #007bff;\n      text-decoration: none;\n      font-size: 13px;\n      font-weight: 500;\n      transition: color 0.2s ease;\n\n      &:hover {\n        color: #0056b3;\n        text-decoration: underline;\n      }\n\n      &::after {\n        content: \" ↗\";\n        font-size: 11px;\n      }\n    }\n  }\n\n  // Responsive design\n  @media (max-width: 768px) {\n    padding: 16px;\n    margin-bottom: 12px;\n\n    .review-header {\n      flex-direction: column;\n      gap: 12px;\n      align-items: flex-start;\n\n      .review-meta {\n        align-items: flex-start;\n        flex-direction: row;\n        gap: 12px;\n      }\n    }\n\n    .review-content {\n      .review-title {\n        font-size: 15px;\n      }\n\n      .review-body {\n        font-size: 13px;\n      }\n    }\n  }\n}\n", "// Trustpilot Business Page Component Styles\n.trustpilot-business-page {\n  background: white;\n  border-radius: 8px;\n  padding: 24px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  border: 1px solid #e9ecef;\n  margin-top: 20px;\n\n  .business-placeholder {\n    text-align: center;\n    padding: 40px 20px;\n    color: #666;\n\n    p {\n      margin: 0;\n      font-style: italic;\n      font-size: 22px;\n    }\n  }\n\n  .business-header {\n    display: flex;\n    gap: 20px;\n    align-items: flex-start;\n\n    .business-logo {\n      position: relative;\n      width: 122px;\n      flex-shrink: 0;\n      border-radius: 8px;\n      overflow: hidden;\n      background-color: #f8f9fa;\n      padding: 8px;\n      box-shadow: rgba(0, 0, 0, 0.02) 0px 1px 3px 0px, rgba(27, 31, 35, 0.15) 0px 0px 0px 1px;\n\n      .business-image {\n        width: 100%;\n        height: 100%;\n        object-fit: contain;\n      }\n\n      .business-initials {\n        width: 100%;\n        height: 100%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background: linear-gradient(135deg, #ff6d00, #ff8f00);\n        color: white;\n        font-size: 28px;\n        font-weight: 700;\n      }\n    }\n\n    .business-info {\n      flex: 1;\n      min-width: 0;\n\n      .business-name-section {\n        display: flex;\n        align-items: center;\n        gap: 12px;\n        margin-bottom: 8px;\n        flex-wrap: wrap;\n\n        .business-name {\n          font-size: 24px;\n          font-weight: 700;\n          color: #333;\n          margin: 0;\n          line-height: 1.2;\n        }\n\n        .business-claimed,\n        .business-verified {\n          background: #28a745;\n          color: white;\n          padding: 2px 8px;\n          border-radius: 12px;\n          font-size: 11px;\n          font-weight: 600;\n          white-space: nowrap;\n        }\n\n        .business-verified {\n          background: #007bff;\n        }\n      }\n\n      .business-website {\n        color: #666;\n        margin: 0 0 4px 0;\n        font-size: 14px;\n      }\n\n      .business-category {\n        color: #888;\n        margin: 0 0 16px 0;\n        font-size: 13px;\n        font-style: italic;\n      }\n\n      .business-rating {\n        margin-bottom: 16px;\n\n        .rating-percentage{\n          display: flex;\n          align-items: center;\n          column-gap: 10px;\n          margin-bottom: 10px;\n\n          .dynamic_stars{\n            width: 200px;\n            line-height: 0;\n\n            img {\n              width: 100%;\n            }\n          }\n\n          > span {\n            font-size: 22px;\n            font-weight: 600;\n          }\n        }\n\n        .review-count {\n          margin-left: 8px;\n          color: #666;\n          font-size: 14px;\n        }\n      }\n\n      .business-links {\n        .business-website-link {\n          // display: inline-flex;\n          // align-items: center;\n          // padding: 8px 16px;\n          background: #007bff;\n          color: white;\n          // text-decoration: none;\n          border-radius: 6px;\n          // font-size: 14px;\n          // font-weight: 500;\n          transition: background-color 0.2s ease;\n\n          &:hover {\n            background: #0056b3;\n            color: white;\n          }\n\n          &::after {\n            content: \" ↗\";\n            margin-left: 4px;\n            font-size: 12px;\n          }\n        }\n      }\n    }\n  }\n\n  .business-stats {\n    margin-top: 20px;\n    padding-top: 20px;\n    border-top: 1px solid #e9ecef;\n    display: flex;\n    gap: 24px;\n    flex-wrap: wrap;\n\n    .stat-item {\n      display: flex;\n      flex-direction: column;\n      gap: 4px;\n\n      .stat-label {\n        font-size: 12px;\n        color: #666;\n        text-transform: uppercase;\n        letter-spacing: 0.5px;\n      }\n\n      .stat-value {\n        font-size: 16px;\n        font-weight: 600;\n        color: #333;\n      }\n    }\n  }\n\n  // Responsive design\n  @media (max-width: 768px) {\n    padding: 20px;\n\n    .business-header {\n      flex-direction: column;\n      gap: 16px;\n      text-align: center;\n\n      .business-logo {\n        align-self: center;\n      }\n\n      .business-info {\n        .business-name-section {\n          justify-content: center;\n\n          .business-name {\n            font-size: 20px;\n          }\n        }\n      }\n    }\n\n    .business-stats {\n      justify-content: center;\n      gap: 16px;\n\n      .stat-item {\n        text-align: center;\n      }\n    }\n  }\n}\n", "// Review List Component Styles\n.review-list {\n  margin-top: 20px;\n  .review-list-header {\n    margin-bottom: 24px;\n\n    .review-list-title {\n      font-size: 20px;\n      font-weight: 600;\n      color: #333;\n      margin: 0 0 16px 0;\n    }\n\n    .review-filters {\n      .rating-filter {\n        display: flex;\n        gap: 8px;\n        flex-wrap: wrap;\n\n        .filter-btn {\n          padding: 6px 12px;\n          border: 1px solid #ddd;\n          background: white;\n          color: #666;\n          border-radius: 20px;\n          font-size: 13px;\n          font-weight: 500;\n          cursor: pointer;\n          transition: all 0.2s ease;\n          white-space: nowrap;\n\n          &:hover {\n            border-color: #007bff;\n            color: #007bff;\n          }\n\n          &.active {\n            background: #007bff;\n            border-color: #007bff;\n            color: white;\n          }\n        }\n      }\n    }\n  }\n\n  .review-list-empty {\n    padding: 40px 20px;\n    color: #666;\n    background: #f8f9fa;\n    border-radius: 8px;\n    border: 1px dashed #ddd;\n    margin-top: 40px;\n\n    p {\n      margin: 0;\n      font-style: italic;\n      font-size: 22px;\n      text-align: center;\n    }\n  }\n  .review-list-content {\n\n    .review-cards {\n      // Individual review cards are styled in _review-card.scss\n    }\n  }\n\n  .review-list-controls {\n    display: flex;\n    justify-content: center;\n    gap: 12px;\n    margin-top: 24px;\n    padding-top: 20px;\n    border-top: 1px solid #e9ecef;\n\n    .load-more-btn,\n    .show-less-btn {\n      padding: 10px 20px;\n      border: 1px solid #007bff;\n      background: white;\n      color: #007bff;\n      border-radius: 6px;\n      font-size: 14px;\n      font-weight: 500;\n      cursor: pointer;\n      transition: all 0.2s ease;\n\n      &:hover {\n        background: #007bff;\n        color: white;\n      }\n    }\n\n    .show-less-btn {\n      border-color: #6c757d;\n      color: #6c757d;\n\n      &:hover {\n        background: #6c757d;\n        color: white;\n      }\n    }\n  }\n\n  .review-summary {\n    text-align: center;\n    margin-top: 16px;\n\n    .review-count-summary {\n      color: #666;\n      font-size: 14px;\n      margin: 0;\n    }\n  }\n\n  // Responsive design\n  @media (max-width: 768px) {\n    .review-list-header {\n      .review-list-title {\n        font-size: 18px;\n      }\n\n      .review-filters {\n        .rating-filter {\n          gap: 6px;\n\n          .filter-btn {\n            padding: 5px 10px;\n            font-size: 12px;\n          }\n        }\n      }\n    }\n\n    .review-list-controls {\n      flex-direction: column;\n      align-items: center;\n\n      .load-more-btn,\n      .show-less-btn {\n        width: 100%;\n        max-width: 280px;\n      }\n    }\n  }\n}\n"], "names": [], "sourceRoot": ""}